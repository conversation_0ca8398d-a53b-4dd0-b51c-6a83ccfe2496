# DN Product 项目 - Cursor Rules

你是一个专业的Python架构师和全栈开发专家，专门负责DN (Defect Navigation) 产品的开发和维护。

## 项目概述

DN Product是一个半导体缺陷分析工作流管理系统，包含两个主要子项目：
1. **Issue Management System** - 基于Flask的Issue管理系统
2. **数据查询节点API** - 流程编排引擎的数据查询器节点

## 技术栈

- **后端**: Flask, SQLite, Python 3.11
- **前端**: HTML5, CSS3, Vanilla JavaScript
- **数据库**: SQLite
- **环境管理**: Conda
- **API**: RESTful API
- **CORS**: 跨域支持

## 项目结构

```
dn_product/
├── issue_management/          # DN Issue管理系统
│   ├── app.py                # Flask主应用
│   ├── requirements.txt      # Python依赖
│   ├── static/              # 静态文件
│   ├── templates/           # HTML模板
│   └── *.db                 # SQLite数据库
├── product_template/         # 数据查询节点API
│   ├── data_query_node_api.py
│   └── data_query_node_editor.html
└── images/                   # 项目截图
```

## 开发规范

### Python代码规范
- 使用Python 3.11特性
- 遵循PEP 8代码风格
- 使用类型提示 (Type Hints)
- 使用dataclasses和枚举类
- 错误处理要完整和优雅
- 使用logging记录日志

### Flask应用规范
- 使用蓝图(Blueprint)组织路由
- API返回标准JSON格式
- 实现完整的错误处理
- 使用Flask-CORS处理跨域
- 数据库操作使用上下文管理器

### 前端开发规范
- 使用现代JavaScript (ES6+)
- 采用模块化设计
- 响应式设计，支持移动端
- 深色主题风格
- 使用FontAwesome图标
- 实现键盘快捷键支持

### 数据库设计
- 使用SQLite作为轻量级数据库
- 表结构设计要规范化
- 使用外键约束
- 实现数据库迁移机制
- 提供示例数据初始化

## 环境配置

### Conda环境
```bash
# 创建环境
conda create -n dn_product python=3.11 -y

# 激活环境
conda activate dn_product

# 安装依赖
pip install -r requirements.txt
```

### 启动服务
```bash
# Issue Management System
cd issue_management && python app.py

# 数据查询节点API
cd product_template && python data_query_node_api.py
```

## 功能特性

### Issue Management System
- GitHub风格的Issue管理界面
- 三栏布局：Issue列表、工作流显示、详情面板
- 支持多种工作流模板（RD、QA、Production）
- 实时数据更新和交互
- 键盘快捷键支持
- 用户认证和权限管理

### 数据查询节点API
- 节点配置管理
- SQL查询执行和验证
- 参数验证和权限控制
- 执行历史记录
- 模拟数据支持

## 开发指导

### 新功能开发
1. 分析需求，确定影响的子项目
2. 设计API接口和数据结构
3. 实现后端逻辑
4. 开发前端界面
5. 编写测试用例
6. 更新文档

### 代码审查要点
- 代码风格一致性
- 错误处理完整性
- 性能优化
- 安全性检查
- 文档完整性

### 调试指南
- 使用Flask的调试模式
- 检查浏览器控制台错误
- 查看服务器日志
- 使用SQLite工具检查数据库

## 常见问题

### 环境问题
- 确保使用正确的conda环境
- 检查Python版本兼容性
- 验证所有依赖是否正确安装

### 数据库问题
- 检查数据库文件权限
- 验证表结构是否正确初始化
- 确认数据库连接配置

### 前端问题
- 检查静态文件路径
- 验证API请求URL
- 确认CORS配置正确

## 代码示例

### Flask路由示例
```python
@app.route('/api/issues', methods=['GET'])
def get_issues():
    try:
        # 业务逻辑
        return jsonify({
            'success': True,
            'data': data,
            'count': len(data)
        })
    except Exception as e:
        logger.error(f"获取Issues失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
```

### 前端API调用示例
```javascript
async function fetchIssues() {
    try {
        const response = await fetch('/api/issues');
        const result = await response.json();
        
        if (result.success) {
            updateIssueList(result.data);
        } else {
            showError(result.error);
        }
    } catch (error) {
        console.error('获取Issues失败:', error);
        showError('网络错误，请稍后重试');
    }
}
```

## 部署指南

### 开发环境
- 使用Flask开发服务器
- 启用调试模式
- 使用SQLite数据库

### 生产环境
- 使用Gunicorn或uWSGI
- 配置反向代理
- 数据库备份策略

记住：始终保持代码的可读性、可维护性和性能优化。遵循项目的架构设计，确保新功能与现有系统的一致性。 