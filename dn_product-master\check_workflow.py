#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查工作流配置
"""

import sqlite3
import json

def check_workflow():
    conn = sqlite3.connect('issue_management/dn_issues.db')
    conn.row_factory = sqlite3.Row
    
    workflow = conn.execute('SELECT id, name, config FROM workflows WHERE id = ?', ('dn-rd',)).fetchone()
    
    if workflow:
        print(f"✅ 找到工作流: {workflow['name']}")
        try:
            config = json.loads(workflow['config'])
            print(f"📊 配置结构: {list(config.keys())}")
            
            if 'nodes' in config:
                print(f"   节点数: {len(config['nodes'])}")
                for i, node in enumerate(config['nodes'][:3]):  # 只显示前3个
                    print(f"   节点{i+1}: {node['icon']} - {node['title']}")
                    
            if 'connections' in config:
                print(f"   连接数: {len(config['connections'])}")
                for i, conn in enumerate(config['connections'][:3]):  # 只显示前3个
                    print(f"   连接{i+1}: {conn['from']} → {conn['to']}")
                    
        except Exception as e:
            print(f"❌ 配置解析错误: {e}")
    else:
        print("❌ 未找到 dn-rd 工作流")
    
    conn.close()

if __name__ == '__main__':
    check_workflow()
