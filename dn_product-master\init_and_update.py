#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化数据库并更新工作流节点位置
"""

import sys
import os
sys.path.append('issue_management')

from issue_management.app import init_database
import sqlite3
import json

def main():
    print("🚀 开始初始化数据库...")
    
    # 初始化数据库
    init_database()
    print("✅ 数据库初始化完成")
    
    # 更新节点位置
    print("🔄 开始更新节点位置...")
    
    db_path = 'issue_management/dn_issues.db'
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    try:
        # 更新 dn-rd 工作流
        workflow = conn.execute('SELECT * FROM workflows WHERE id = ?', ('dn-rd',)).fetchone()
        
        if workflow:
            config = json.loads(workflow['config'])
            
            # 新的节点位置
            position_updates = {
                'hold_lot_query': {'x': 250, 'y': 50},
                'hold_lot_display': {'x': 250, 'y': 180},
                'case_info': {'x': 250, 'y': 310},
                'scan_info_query': {'x': 50, 'y': 440},
                'lot_history_query': {'x': 180, 'y': 440},
                'map_gallery_query': {'x': 320, 'y': 440},
                'trend_chart_query': {'x': 450, 'y': 440},
                'scan_info_display': {'x': 50, 'y': 570},
                'lot_history_display': {'x': 180, 'y': 570},
                'map_gallery_display': {'x': 320, 'y': 570},
                'trend_chart_display': {'x': 450, 'y': 570},
                'case_confirm': {'x': 250, 'y': 700},
                'ye_comment': {'x': 180, 'y': 830},
                'ppt_preview': {'x': 320, 'y': 830},
                'send_email': {'x': 250, 'y': 960}
            }
            
            # 更新节点位置
            for node in config.get('nodes', []):
                node_id = node.get('id')
                if node_id in position_updates:
                    old_y = node.get('position', {}).get('y', 0)
                    new_y = position_updates[node_id]['y']
                    node['position'] = position_updates[node_id]
                    print(f"  📍 {node_id}: y={old_y} → y={new_y}")
            
            # 保存更新
            conn.execute(
                'UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                (json.dumps(config), 'dn-rd')
            )
            
            # 同样更新 dn-production
            production_workflow = conn.execute('SELECT * FROM workflows WHERE id = ?', ('dn-production',)).fetchone()
            if production_workflow:
                production_config = json.loads(production_workflow['config'])
                for node in production_config.get('nodes', []):
                    node_id = node.get('id')
                    if node_id in position_updates:
                        node['position'] = position_updates[node_id]
                
                conn.execute(
                    'UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    (json.dumps(production_config), 'dn-production')
                )
            
            conn.commit()
            print("✅ 节点位置更新完成！")
            print("🎉 现在重启应用，节点间距应该更合理了")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    main()
