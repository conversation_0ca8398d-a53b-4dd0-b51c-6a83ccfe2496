# DN Issue Management System - HTTPS配置指南

## 快速开始

### 1. 使用HTTPS启动（推荐）

```bash
# 使用默认HTTPS模式启动
python start_server.py

# 或者直接运行主应用（会自动生成SSL证书）
python app.py
```

访问地址：`https://localhost:5443`

### 2. 使用HTTP启动

```bash
# HTTP模式启动
python start_server.py --mode http

# 或者设置环境变量
export FORCE_HTTPS=false
python app.py
```

访问地址：`http://localhost:5000`

## 启动选项

### 使用启动脚本

```bash
# HTTPS模式（默认）
python start_server.py --mode https

# HTTP模式
python start_server.py --mode http

# 指定端口
python start_server.py --mode https --port 8443

# 生产环境
python start_server.py --mode https --env production

# 仅生成SSL证书
python start_server.py --generate-cert
```

### 环境变量配置

```bash
# 强制HTTPS
export FORCE_HTTPS=true

# 设置运行环境
export FLASK_CONFIG=production

# 设置密钥
export SECRET_KEY=your-super-secret-key
```

## SSL证书

### 自签名证书（开发环境）

系统会自动生成自签名证书：
- `cert.pem` - SSL证书文件
- `key.pem` - 私钥文件

**注意：** 浏览器会显示"不安全"警告，这是正常的。点击"高级"→"继续访问"即可。

### 生产环境证书

在生产环境中，建议使用正式的SSL证书：

1. 从CA机构购买SSL证书
2. 或使用Let's Encrypt免费证书
3. 将证书文件替换为 `cert.pem` 和 `key.pem`

## 配置文件

### config.py 配置说明

```python
# 开发环境配置
class DevelopmentConfig(Config):
    DEBUG = True
    FORCE_HTTPS = False  # 开发环境可选择不强制HTTPS

# 生产环境配置
class ProductionConfig(Config):
    DEBUG = False
    FORCE_HTTPS = True   # 生产环境强制HTTPS
```

### 端口配置

- HTTP端口：5000
- HTTPS端口：5443

可以在 `config.py` 中修改默认端口。

## 安全功能

### 1. 自动HTTPS重定向

在生产环境中，HTTP请求会自动重定向到HTTPS。

### 2. 安全头部

自动添加安全HTTP头部：
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security` (HTTPS时)

### 3. Session安全

- 24小时session过期时间
- 安全的session配置

## 故障排除

### 1. SSL证书生成失败

确保系统已安装OpenSSL：

```bash
# Ubuntu/Debian
sudo apt-get install openssl

# CentOS/RHEL
sudo yum install openssl

# macOS
brew install openssl
```

### 2. 端口被占用

```bash
# 查看端口占用
netstat -tulpn | grep :5443

# 或使用其他端口
python start_server.py --port 8443
```

### 3. 浏览器证书警告

对于自签名证书，这是正常现象：
1. 点击"高级"或"Advanced"
2. 点击"继续访问"或"Proceed to localhost"

### 4. 防火墙问题

确保防火墙允许相应端口：

```bash
# Ubuntu/Debian
sudo ufw allow 5443

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=5443/tcp
sudo firewall-cmd --reload
```

## 部署建议

### 开发环境
- 使用自签名证书
- 可以使用HTTP模式
- 启用DEBUG模式

### 生产环境
- 使用正式SSL证书
- 强制HTTPS
- 关闭DEBUG模式
- 设置强密钥
- 配置反向代理（Nginx/Apache）

### 示例Nginx配置

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    location / {
        proxy_pass https://localhost:5443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 常见问题

**Q: 为什么浏览器显示"不安全"？**
A: 这是因为使用了自签名证书。在生产环境中使用正式证书可以解决这个问题。

**Q: 如何在生产环境中使用？**
A: 设置环境变量 `FLASK_CONFIG=production` 并使用正式SSL证书。

**Q: 可以同时启动HTTP和HTTPS吗？**
A: 当前版本不支持。建议在生产环境中只使用HTTPS。

**Q: 如何更改默认端口？**
A: 使用 `--port` 参数或修改 `config.py` 中的端口配置。 