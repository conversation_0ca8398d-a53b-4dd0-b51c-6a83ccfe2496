# DN Issue Management System

基于Flask和JavaScript的DN（Defect Navigation）Issue管理系统，实现了类似GitHub Issue的界面风格，用于管理半导体缺陷分析工作流。

## 功能特性

### 🎯 核心功能
- **Issue管理**: 创建、查看、编辑、删除Issues
- **工作流管理**: 支持多种工作流模板（RD、QA、Production）
- **三栏布局**: 左侧Issue列表、中间工作流显示、右侧详情面板
- **响应式设计**: 适配不同屏幕尺寸
- **实时交互**: 前后端分离，支持实时数据更新

### 🎨 界面特性
- **GitHub风格**: 深色主题，现代化UI设计
- **可折叠面板**: 右侧详情面板支持折叠/展开
- **高亮显示**: Issue和工作流节点支持选中高亮
- **动画效果**: 平滑的过渡动画和加载效果

### ⌨️ 交互特性
- **键盘快捷键**: 
  - `Ctrl/Cmd + N`: 创建新Issue
  - `Ctrl/Cmd + R`: 刷新数据
  - `空格键`: 切换右侧面板
  - `ESC`: 取消选择
- **鼠标交互**: 点击选择、悬停高亮
- **通知系统**: 成功/错误消息提示

## 技术架构

### 后端技术栈
- **Flask**: Python Web框架
- **SQLite**: 轻量级数据库
- **Flask-CORS**: 跨域资源共享支持
- **RESTful API**: 标准化API接口

### 前端技术栈
- **HTML5 + CSS3**: 现代化页面结构和样式
- **Vanilla JavaScript**: 原生JS，无框架依赖
- **FontAwesome**: 图标库
- **Fetch API**: 异步数据获取

### 数据库设计
```sql
-- Issues表
CREATE TABLE issues (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'open',
    assignee TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    workflow_id TEXT DEFAULT 'dn-rd',
    labels TEXT,
    priority TEXT DEFAULT 'medium'
);

-- 工作流步骤表
CREATE TABLE workflow_steps (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue_id INTEGER,
    step_id TEXT NOT NULL,
    step_name TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    config TEXT,
    result TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (issue_id) REFERENCES issues (id)
);

-- 工作流模板表
CREATE TABLE workflows (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    config TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 快速开始

### 环境要求
- Python 3.7+
- pip包管理器

### 安装步骤

1. **克隆/下载项目**
```bash
# 进入项目目录
cd issue_management
```

2. **安装Python依赖**
```bash
pip install -r requirements.txt
```

3. **启动应用**
```bash
python app.py
```

4. **访问应用**
```
打开浏览器访问: http://localhost:3000
```

### 首次运行
应用首次启动时会自动：
- 创建SQLite数据库文件 `dn_issues.db`
- 初始化数据库表结构
- 插入示例数据和工作流模板

## API接口文档

### Issues API

#### 获取所有Issues
```http
GET /api/issues
```

#### 创建新Issue
```http
POST /api/issues
Content-Type: application/json

{
    "title": "Issue标题",
    "description": "Issue描述",
    "status": "open",
    "assignee": "用户名",
    "workflow_id": "dn-rd",
    "labels": ["标签1", "标签2"],
    "priority": "high"
}
```

#### 获取特定Issue
```http
GET /api/issues/{id}
```

#### 更新Issue
```http
PUT /api/issues/{id}
Content-Type: application/json

{
    "title": "更新的标题",
    "status": "in-progress"
}
```

#### 删除Issue
```http
DELETE /api/issues/{id}
```

### 工作流API

#### 获取所有工作流
```http
GET /api/workflows
```

#### 获取特定工作流
```http
GET /api/workflows/{workflow_id}
```

### 工作流步骤API

#### 获取Issue的工作流步骤
```http
GET /api/issues/{issue_id}/workflow-steps
```

#### 创建工作流步骤
```http
POST /api/issues/{issue_id}/workflow-steps
Content-Type: application/json

{
    "step_id": "step-001",
    "step_name": "步骤名称",
    "status": "pending",
    "config": {},
    "result": {}
}
```

### 健康检查
```http
GET /api/health
```

## 工作流配置

系统预置了三种工作流模板：

### 1. DN 目标工作流-RD (dn-rd)
研发流程，包含：
- **并行-1A**: YE CASE智能业务流程、HOLD LOT LIST查询
- **并行-1B**: ROBOT ANALYSIS算法分析、DEFECT TREND趋势分析
- **并行-1C**: LOT HISTORY查询、DEFECT MAP绘制与展示
- **并行-1D**: TOOL COMMONALITY相关性分析

### 2. DN 目标工作流-QA (dn-qa)
质量保证流程，包含：
- QA质量检查
- 测试验证

### 3. DN 目标工作流-Production (dn-production)
生产流程，包含：
- 生产部署
- 监控系统

## 项目结构

```
issue_management/
├── app.py                 # Flask主应用
├── requirements.txt       # Python依赖
├── README.md             # 项目说明
├── index.html            # 主页面
├── static/
│   ├── style.css         # 样式文件
│   └── script.js         # 前端JavaScript
└── dn_issues.db          # SQLite数据库（运行时生成）
```

## 自定义配置

### 添加新的工作流模板
在 `app.py` 的 `init_database()` 函数中添加新的工作流配置：

```python
{
    'id': 'custom-workflow',
    'name': '自定义工作流',
    'description': '自定义工作流描述',
    'config': json.dumps({
        'sections': [
            {
                'title': '自定义阶段',
                'steps': [
                    {
                        'id': 'custom-step',
                        'icon': 'CS',
                        'type': 'custom',
                        'title': '自定义步骤',
                        'description': '自定义步骤描述'
                    }
                ]
            }
        ]
    })
}
```

### 修改端口
在 `app.py` 最后一行修改端口号：

```python
app.run(debug=True, host='0.0.0.0', port=YOUR_PORT)
```

## 部署说明

### 开发环境
直接运行 `python app.py` 即可启动开发服务器

### 生产环境
建议使用Gunicorn等WSGI服务器：

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:3000 app:app
```

或使用Docker部署（需要创建Dockerfile）

## 故障排除

### 端口占用
如果3000端口被占用，可以：
1. 修改 `app.py` 中的端口号
2. 或者停止占用端口的进程

### 数据库权限问题
确保应用有权限在当前目录创建和修改 `dn_issues.db` 文件

### 跨域问题
如果遇到CORS问题，检查Flask-CORS配置是否正确

## 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目基于MIT许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 更新日志

### v1.0.0 (2024-01-16)
- ✨ 初始版本发布
- 🎯 实现基础Issue管理功能
- 🎨 GitHub风格的现代化UI
- ⚡ 三栏响应式布局
- 🔧 RESTful API支持
- 📱 键盘快捷键支持
- 🔄 实时数据同步

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- Email: <EMAIL>

---

**DN Issue Management System** - 让缺陷分析工作流管理更加高效！ 