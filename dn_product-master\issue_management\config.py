import os
from datetime import timedelta

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # 数据库配置
    DATABASE_PATH = os.path.join(os.path.dirname(__file__), 'dn_issues.db')
    
    # Session配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # SSL配置
    SSL_CERT_FILE = os.path.join(os.path.dirname(__file__), 'cert.pem')
    SSL_KEY_FILE = os.path.join(os.path.dirname(__file__), 'key.pem')
    
    # 服务器配置
    HOST = '0.0.0.0'
    HTTP_PORT = 5000
    HTTPS_PORT = 5443
    
    # 安全配置
    FORCE_HTTPS = os.environ.get('FORCE_HTTPS', 'false').lower() == 'true'
    SECURE_HEADERS = True

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FORCE_HTTPS = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FORCE_HTTPS = True
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'please-set-a-real-secret-key'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    FORCE_HTTPS = False

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
} 