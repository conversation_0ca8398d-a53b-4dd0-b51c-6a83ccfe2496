<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DN 流程编排引擎 - 节点编辑器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/node_editor.css">
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <div class="top-nav">
            <div class="nav-left">
                <h1><i class="fas fa-project-diagram"></i> DN 流程编排引擎</h1>
            </div>
            <div class="nav-right">
                <button class="btn btn-secondary" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i> 返回
                </button>
                <button class="btn btn-primary" onclick="saveWorkflow()">
                    <i class="fas fa-save"></i> 保存流程
                </button>
            </div>
        </div>

        <div class="main-layout">
            <!-- 左侧节点库 -->
            <div class="node-library">
                <div class="library-header">
                    <h3><i class="fas fa-cubes"></i> 节点库</h3>
                    <button class="btn btn-sm btn-primary" onclick="toggleLibrary()">
                        <i class="fas fa-compress-alt"></i>
                    </button>
                </div>
                
                <div class="library-content">
                    <!-- 数据查询节点 -->
                    <div class="node-category">
                        <h4><i class="fas fa-database"></i> 数据查询节点</h4>
                        <div class="node-items">
                            <div class="node-item" draggable="true" data-node-type="case-info">
                                <div class="node-icon">CI</div>
                                <div class="node-info">
                                    <div class="node-title">Case Info</div>
                                    <div class="node-desc">Case基础信息查询</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="scan-info-query">
                                <div class="node-icon">SQ</div>
                                <div class="node-info">
                                    <div class="node-title">Scan Info Query</div>
                                    <div class="node-desc">Lot检测信息查询</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="lot-history-query">
                                <div class="node-icon">LQ</div>
                                <div class="node-info">
                                    <div class="node-title">Lot History Query</div>
                                    <div class="node-desc">Lot履历信息查询</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据展示节点 -->
                    <div class="node-category">
                        <h4><i class="fas fa-chart-bar"></i> 数据展示节点</h4>
                        <div class="node-items">
                            <div class="node-item" draggable="true" data-node-type="scan-info-table">
                                <div class="node-icon">ST</div>
                                <div class="node-info">
                                    <div class="node-title">Scan Info Table</div>
                                    <div class="node-desc">垂直表展示</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="lot-history-table">
                                <div class="node-icon">LT</div>
                                <div class="node-info">
                                    <div class="node-title">Lot History Table</div>
                                    <div class="node-desc">履历表展示</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="map-gallery">
                                <div class="node-icon">MG</div>
                                <div class="node-info">
                                    <div class="node-title">Map Gallery</div>
                                    <div class="node-desc">Map图和Image展示</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="trend-chart">
                                <div class="node-icon">TC</div>
                                <div class="node-info">
                                    <div class="node-title">Trend Chart</div>
                                    <div class="node-desc">趋势图表展示</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析处理节点 -->
                    <div class="node-category">
                        <h4><i class="fas fa-brain"></i> 分析处理节点</h4>
                        <div class="node-items">
                            <div class="node-item" draggable="true" data-node-type="defect-analysis">
                                <div class="node-icon">DA</div>
                                <div class="node-info">
                                    <div class="node-title">Defect Analysis</div>
                                    <div class="node-desc">缺陷分析处理</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="pattern-match">
                                <div class="node-icon">PM</div>
                                <div class="node-info">
                                    <div class="node-title">Pattern Match</div>
                                    <div class="node-desc">模式匹配分析</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="ai-diagnosis">
                                <div class="node-icon">AI</div>
                                <div class="node-info">
                                    <div class="node-title">AI Diagnosis</div>
                                    <div class="node-desc">AI智能诊断</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 流程控制节点 -->
                    <div class="node-category">
                        <h4><i class="fas fa-code-branch"></i> 流程控制节点</h4>
                        <div class="node-items">
                            <div class="node-item" draggable="true" data-node-type="condition">
                                <div class="node-icon">IF</div>
                                <div class="node-info">
                                    <div class="node-title">Condition</div>
                                    <div class="node-desc">条件判断</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="loop">
                                <div class="node-icon">LP</div>
                                <div class="node-info">
                                    <div class="node-title">Loop</div>
                                    <div class="node-desc">循环处理</div>
                                </div>
                            </div>
                            <div class="node-item" draggable="true" data-node-type="parallel">
                                <div class="node-icon">PR</div>
                                <div class="node-info">
                                    <div class="node-title">Parallel</div>
                                    <div class="node-desc">并行处理</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间流程画布 -->
            <div class="workflow-canvas">
                <div class="canvas-header">
                    <div class="canvas-title">
                        <h3 id="workflowTitle" contenteditable="true">新建流程</h3>
                        <span class="edit-hint">双击编辑</span>
                    </div>
                    <div class="canvas-tools">
                        <button class="btn btn-sm btn-secondary" onclick="zoomOut()">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <span class="zoom-level">100%</span>
                        <button class="btn btn-sm btn-secondary" onclick="zoomIn()">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="resetZoom()">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="clearCanvas()">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="canvas-container" id="canvasContainer">
                    <div class="canvas-grid" id="canvasGrid">
                        <!-- 流程节点将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 右侧属性面板 -->
            <div class="property-panel" id="propertyPanel">
                <div class="panel-header">
                    <h3><i class="fas fa-cog"></i> 节点属性</h3>
                    <button class="btn btn-sm btn-secondary" onclick="closePropertyPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="panel-content" id="panelContent">
                    <div class="empty-state">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>请选择一个节点来编辑属性</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 节点配置模态框 -->
    <div class="modal" id="nodeConfigModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">节点配置</h3>
                <button class="btn btn-sm btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveNodeConfig()">保存</button>
            </div>
        </div>
    </div>

    <!-- 连接线绘制SVG -->
    <svg class="connection-svg" id="connectionSvg">
        <!-- 连接线将在这里绘制 -->
    </svg>

    <script src="/static/node_editor.js"></script>
</body>
</html> 