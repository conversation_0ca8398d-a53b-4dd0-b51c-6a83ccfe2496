#!/usr/bin/env python3
"""
简化的启动脚本，避免SSL问题
"""

from flask import Flask, jsonify, request, send_from_directory, session, redirect, render_template
from flask_cors import CORS
import sqlite3
import json
from datetime import datetime, timedelta
import os
import random
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps

app = Flask(__name__)
CORS(app, supports_credentials=True)
app.secret_key = 'your-secret-key-here'

# 数据库配置
DATABASE = 'dn_issues.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """初始化数据库"""
    from app import init_database as original_init
    original_init()

if __name__ == '__main__':
    init_database()
    print("DN Issue Management System 启动中...")
    print("访问地址: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)