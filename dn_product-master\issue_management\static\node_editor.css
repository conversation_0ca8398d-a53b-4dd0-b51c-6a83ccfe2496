/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f5f6fa;
    color: #2f3542;
    overflow: hidden;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-nav {
    background: #fff;
    border-bottom: 1px solid #e1e8ed;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.nav-left h1 {
    font-size: 18px;
    font-weight: 600;
    color: #2f3542;
}

.nav-left i {
    color: #3742fa;
    margin-right: 8px;
}

.nav-right {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background: #3742fa;
    color: white;
}

.btn-primary:hover {
    background: #2f32e2;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f1f2f6;
    color: #57606f;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e4e6ea;
    border-color: #ccc;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 主布局 */
.main-layout {
    flex: 1;
    display: flex;
    height: calc(100vh - 60px);
    overflow: hidden;
}

/* 左侧节点库 */
.node-library {
    width: 280px;
    background: #fff;
    border-right: 1px solid #e1e8ed;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.library-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
}

.library-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2f3542;
}

.library-header i {
    color: #3742fa;
    margin-right: 8px;
}

.library-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.node-category {
    margin-bottom: 24px;
}

.node-category h4 {
    font-size: 14px;
    font-weight: 600;
    color: #57606f;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e1e8ed;
}

.node-category i {
    color: #3742fa;
    margin-right: 6px;
}

.node-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.node-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    cursor: grab;
    transition: all 0.2s ease;
    user-select: none;
}

.node-item:hover {
    background: #e9ecef;
    border-color: #3742fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(55, 66, 250, 0.15);
}

.node-item:active {
    cursor: grabbing;
}

.node-icon {
    width: 36px;
    height: 36px;
    background: #3742fa;
    color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-right: 12px;
    flex-shrink: 0;
}

.node-info {
    flex: 1;
}

.node-title {
    font-size: 14px;
    font-weight: 600;
    color: #2f3542;
    margin-bottom: 2px;
}

.node-desc {
    font-size: 12px;
    color: #747d8c;
    line-height: 1.3;
}

/* 中间流程画布 */
.workflow-canvas {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    overflow: hidden;
}

.canvas-header {
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.canvas-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.canvas-title h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2f3542;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.canvas-title h3:hover {
    background: #f1f2f6;
}

.canvas-title h3:focus {
    outline: none;
    background: #fff;
    border: 1px solid #3742fa;
}

.edit-hint {
    font-size: 12px;
    color: #a4b0be;
}

.canvas-tools {
    display: flex;
    align-items: center;
    gap: 8px;
}

.zoom-level {
    font-size: 12px;
    color: #57606f;
    padding: 0 8px;
}

.canvas-container {
    flex: 1;
    position: relative;
    overflow: auto;
}

.canvas-grid {
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle, #ddd 1px, transparent 1px);
    background-size: 20px 20px;
    position: relative;
    min-height: 800px;
    min-width: 1200px;
}

/* 画布中的节点 */
.canvas-node {
    position: absolute;
    width: 160px;
    background: #fff;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.canvas-node:hover {
    border-color: #3742fa;
    box-shadow: 0 4px 16px rgba(55, 66, 250, 0.2);
    transform: translateY(-2px);
}

.canvas-node.selected {
    border-color: #3742fa;
    box-shadow: 0 4px 16px rgba(55, 66, 250, 0.3);
}

.canvas-node.dragging {
    opacity: 0.8;
    transform: rotate(5deg);
}

.canvas-node-header {
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e8ed;
    border-radius: 6px 6px 0 0;
}

.canvas-node-icon {
    width: 28px;
    height: 28px;
    background: #3742fa;
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    flex-shrink: 0;
}

.canvas-node-title {
    font-size: 13px;
    font-weight: 600;
    color: #2f3542;
    flex: 1;
}

.canvas-node-body {
    padding: 12px;
}

.canvas-node-desc {
    font-size: 11px;
    color: #747d8c;
    line-height: 1.4;
    margin-bottom: 8px;
}

.canvas-node-status {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    background: #f1f2f6;
    color: #57606f;
    display: inline-block;
}

.canvas-node-status.configured {
    background: #2ed573;
    color: white;
}

.canvas-node-status.error {
    background: #ff4757;
    color: white;
}

/* 连接点 */
.connection-point {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #3742fa;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: crosshair;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
}

.canvas-node:hover .connection-point {
    opacity: 1;
}

.connection-point.input {
    top: 50%;
    left: -6px;
    transform: translateY(-50%);
}

.connection-point.output {
    top: 50%;
    right: -6px;
    transform: translateY(-50%);
}

.connection-point:hover {
    background: #2f32e2;
    transform: scale(1.2) translateY(-50%);
}

/* 连接线SVG */
.connection-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.connection-line {
    stroke: #3742fa;
    stroke-width: 2;
    fill: none;
    marker-end: url(#arrowhead);
}

.connection-line.temporary {
    stroke: #a4b0be;
    stroke-dasharray: 5,5;
}

/* 右侧属性面板 */
.property-panel {
    width: 320px;
    background: #fff;
    border-left: 1px solid #e1e8ed;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
}

.panel-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2f3542;
}

.panel-header i {
    color: #3742fa;
    margin-right: 8px;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #a4b0be;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.5;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #2f3542;
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3742fa;
    box-shadow: 0 0 0 3px rgba(55, 66, 250, 0.1);
}

.form-control[readonly] {
    background: #f8f9fa;
    color: #57606f;
}

.form-textarea {
    min-height: 80px;
    resize: vertical;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #3742fa;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 参数列表 */
.param-list {
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    overflow: hidden;
}

.param-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e1e8ed;
    background: #fff;
}

.param-item:last-child {
    border-bottom: none;
}

.param-name {
    flex: 1;
    font-size: 14px;
    color: #2f3542;
}

.param-required {
    margin-left: 12px;
}

.param-actions {
    margin-left: 12px;
    display: flex;
    gap: 4px;
}

.param-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.param-btn-settings {
    background: #f1f2f6;
    color: #57606f;
}

.param-btn-settings:hover {
    background: #e4e6ea;
    color: #2f3542;
}

.param-btn-remove {
    background: #ff4757;
    color: white;
}

.param-btn-remove:hover {
    background: #ff3838;
}

.add-param-btn {
    width: 100%;
    padding: 12px;
    border: 1px dashed #a4b0be;
    border-radius: 6px;
    background: #f8f9fa;
    color: #57606f;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 12px;
}

.add-param-btn:hover {
    border-color: #3742fa;
    color: #3742fa;
    background: #fff;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2f3542;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: 60vh;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e1e8ed;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #f8f9fa;
}

/* 权限配置 */
.permission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-top: 12px;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    background: #f8f9fa;
}

.permission-item input[type="checkbox"] {
    margin: 0;
}

.permission-item label {
    font-size: 14px;
    color: #2f3542;
    cursor: pointer;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .node-library {
        width: 240px;
    }
    
    .property-panel {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .main-layout {
        flex-direction: column;
    }
    
    .node-library,
    .property-panel {
        width: 100%;
        height: 200px;
    }
    
    .workflow-canvas {
        flex: 1;
        min-height: 400px;
    }
}

/* 拖拽提示 */
.drag-over {
    background: rgba(55, 66, 250, 0.1);
    border: 2px dashed #3742fa;
}

.drag-placeholder {
    position: absolute;
    width: 160px;
    height: 120px;
    border: 2px dashed #3742fa;
    border-radius: 8px;
    background: rgba(55, 66, 250, 0.1);
    pointer-events: none;
    opacity: 0.7;
}

/* 工具提示 */
.tooltip {
    position: absolute;
    background: #2f3542;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
}

.tooltip.show {
    opacity: 1;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #2f3542 transparent transparent transparent;
}

/* 动画效果 */
@keyframes nodeAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.canvas-node.new {
    animation: nodeAppear 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f2f6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #a4b0be;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #747d8c;
}

/* 加载状态 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3742fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 成功/错误状态 */
.success {
    color: #2ed573;
}

.error {
    color: #ff4757;
}

.warning {
    color: #ffa502;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

.collapsed {
    width: 60px;
}

.collapsed .library-content,
.collapsed .edit-hint {
    display: none;
}

.collapsed .node-info {
    display: none;
}

.collapsed .node-item {
    justify-content: center;
    padding: 8px;
} 