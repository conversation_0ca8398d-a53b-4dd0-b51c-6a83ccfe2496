// 全局变量
let currentUser = null;
let currentPage = 'dashboard';
let currentPageNum = 1;
let totalPages = 1;
let currentQuery = {};
let selectedCase = null;
let sidebarCollapsed = false;

// 图表实例管理
let chartInstances = {
    casesTrendChart: null,
    toolDistributionChart: null,
    statusDistributionChart: null,
    defectClassChart: null
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    checkUserAuth();
});

// 检查用户认证状态
async function checkUserAuth() {
    try {
        const response = await fetch('/api/user');
        if (response.ok) {
            const data = await response.json();
            currentUser = data.user;
            updateUserInfo();
            initializeApp();
        } else {
            // 未登录，重定向到登录页面
            window.location.href = '/login';
        }
    } catch (error) {
        console.error('认证检查失败:', error);
        window.location.href = '/login';
    }
}

// 更新用户信息显示
function updateUserInfo() {
    const userElement = document.getElementById('currentUser');
    if (userElement && currentUser) {
        userElement.textContent = `${currentUser.username} (${currentUser.module})`;
    }
}

// 登出功能
async function logout() {
    try {
        const response = await fetch('/api/logout', {
            method: 'POST'
        });
        
        if (response.ok) {
            window.location.href = '/login';
        } else {
            console.error('登出失败');
        }
    } catch (error) {
        console.error('登出错误:', error);
    }
}

// 初始化应用
function initializeApp() {
    console.log('Initializing app for user:', currentUser);
    
    // 运行页面结构测试
    testPageStructure();
    
    // 初始化侧边栏状态
    initSidebarState();
    
    // 初始化导航
    initNavigation();
    
    // 默认显示Dashboard
    switchPage('dashboard');
}

// 初始化导航
function initNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const page = this.getAttribute('data-page');
            switchPage(page);
        });
    });
}

// 页面切换
function switchPage(page) {
    console.log('=== SWITCH PAGE DEBUG ===');
    console.log('Switching to page:', page);
    
    // 更新导航状态
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    const navItem = document.querySelector(`[data-page="${page}"]`);
    if (navItem) {
        navItem.classList.add('active');
        console.log('Navigation item found and activated:', navItem);
    } else {
        console.error('Navigation item not found for page:', page);
    }
    
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(pageContent => {
        pageContent.classList.remove('active');
        console.log('Hiding page:', pageContent.id);
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(`${page}-page`);
    console.log('Looking for target page with ID:', `${page}-page`);
    console.log('Target page found:', targetPage);
    
    if (targetPage) {
        targetPage.classList.add('active');
        console.log('Target page activated:', targetPage.id);
    } else {
        console.error('Target page not found! Expected ID:', `${page}-page`);
        
        // 列出所有可用的页面
        const allPages = document.querySelectorAll('.page-content');
        console.log('Available pages:');
        allPages.forEach(p => {
            console.log('  - ID:', p.id, 'Classes:', p.className);
        });
    }
    
    currentPage = page;
    console.log('Current page set to:', currentPage);
    
    // 根据页面类型加载相应内容
    switch(page) {
        case 'dashboard':
            console.log('Initializing Dashboard...');
            initDashboard();
            break;
        case 'cases':
            console.log('Initializing Cases page...');
            initCasesPage();
            break;
        case 'workflow':
            console.log('Initializing Workflow page...');
            // 工作流页面初始化
            break;
        case 'analytics':
            console.log('Initializing Analytics page...');
            // 分析页面初始化
            break;
        case 'settings':
            console.log('Initializing Settings page...');
            // 设置页面初始化
            break;
        default:
            console.warn('Unknown page:', page);
    }
    
    console.log('=== SWITCH PAGE DEBUG END ===');
}

// 初始化Dashboard
function initDashboard() {
    console.log('Initializing Dashboard...');
    loadDefectCasesStats();
}

// 加载缺陷案例统计数据
async function loadDefectCasesStats() {
    try {
        const response = await fetch('/api/defect-cases/stats');
        if (response.ok) {
            const stats = await response.json();
            updateDashboardStats(stats);
            updateDashboardCharts(stats);
            updateFilterOptions(stats);
            updateQueryStatusIndicator(false, stats.total_cases);
        } else {
            console.error('加载统计数据失败');
        }
    } catch (error) {
        console.error('加载统计数据错误:', error);
    }
}

// 更新Dashboard统计数据
function updateDashboardStats(stats) {
    document.getElementById('totalCases').textContent = stats.total_cases || 0;
    document.getElementById('pendingCases').textContent = stats.pending_cases || 0;
    document.getElementById('inProgressCases').textContent = stats.in_progress_cases || 0;
    document.getElementById('completedCases').textContent = stats.completed_cases || 0;
}

// 更新筛选选项
function updateFilterOptions(stats) {
    // 更新工具选项
    const toolSelect = document.getElementById('toolSelect');
    if (toolSelect && stats.tool_breakdown) {
        toolSelect.innerHTML = '<option value="">请选择Tool</option>';
        Object.keys(stats.tool_breakdown).forEach(tool => {
            const option = document.createElement('option');
            option.value = tool;
            option.textContent = tool;
            toolSelect.appendChild(option);
        });
    }
    
    // 更新缺陷类别选项
    const defectClassSelect = document.getElementById('defectClass');
    if (defectClassSelect && stats.defect_class_breakdown) {
        defectClassSelect.innerHTML = '<option value="">请选择Defect Class</option>';
        Object.keys(stats.defect_class_breakdown).forEach(defectClass => {
            const option = document.createElement('option');
            option.value = defectClass;
            option.textContent = defectClass;
            defectClassSelect.appendChild(option);
        });
    }
}

// 初始化Cases页面
function initCasesPage() {
    console.log('Initializing Cases page');
    
    // 移除可能存在的面板标签
    removePanelTabs();
    
    // 清除选择状态
    selectedCase = null;
    
    // 初始化右侧面板状态
    const caseInfoPanel = document.getElementById('caseInfoPanel');
    const stepsContainer = document.getElementById('stepsContainer');
    const caseInfoSection = document.getElementById('caseInfoSection');
    const panelTitle = document.getElementById('panelTitle');
    
    // 默认显示工作流节点详情，隐藏Case信息面板
    if (caseInfoPanel) {
        caseInfoPanel.style.display = 'none';
    }
    
    if (stepsContainer) {
        stepsContainer.style.display = 'block';
    }
    
    if (caseInfoSection) {
        caseInfoSection.style.display = 'none';
    }
    
    if (panelTitle) {
        panelTitle.textContent = '工作流步骤';
    }
    
    // 加载Cases数据
    loadUserCases();
    
    // 加载工作流
    loadWorkflows();
    
    // 初始化右侧面板折叠状态
    const rightPanel = document.getElementById('rightPanel');
    const casesLayout = document.getElementById('casesLayout');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (rightPanel && casesLayout && toggleIcon) {
        const isCollapsed = localStorage.getItem('rightPanelCollapsed') === 'true';
        if (isCollapsed) {
            rightPanel.classList.add('collapsed');
            casesLayout.classList.add('right-collapsed');
            toggleIcon.classList.remove('fa-chevron-right');
            toggleIcon.classList.add('fa-chevron-left');
        }
    }
}

// 加载用户相关的案例
async function loadUserCases() {
    try {
        console.log('Loading user cases...');
        const response = await fetch('/api/defect-cases?page=1&page_size=20');
        if (response.ok) {
            const data = await response.json();
            console.log('Cases data loaded:', data);
            renderCasesList(data.cases || []);
            console.log(`Loaded ${(data.cases || []).length} cases for user:`, currentUser.username);
        } else {
            console.error('加载案例失败:', response.status, response.statusText);
            renderCasesError('加载案例失败: ' + response.statusText);
        }
    } catch (error) {
        console.error('加载案例错误:', error);
        renderCasesError('网络错误: ' + error.message);
    }
}

// 渲染案例列表
function renderCasesList(cases) {
    console.log('Rendering cases list:', cases.length, 'cases');
    const casesContainer = document.getElementById('casesContainer');
    if (!casesContainer) {
        console.error('Cases container not found!');
        return;
    }
    
    casesContainer.innerHTML = '';
    
    if (!cases || cases.length === 0) {
        casesContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <p>暂无案例数据</p>
                <p>当前用户: ${currentUser ? currentUser.username : 'Unknown'} (${currentUser ? currentUser.module : 'Unknown'} module)</p>
                <button class="btn btn-primary" onclick="createNewIssue()">
                    <i class="fas fa-plus"></i> 创建第一个Case
                </button>
            </div>
        `;
        return;
    }
    
    cases.forEach(case_ => {
        const caseElement = createCaseElement(case_);
        casesContainer.appendChild(caseElement);
    });
}

// 渲染案例加载错误
function renderCasesError(errorMessage) {
    const casesContainer = document.getElementById('casesContainer');
    if (!casesContainer) return;
    
    casesContainer.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: #f85149;"></i>
            <p>加载失败</p>
            <p style="color: #f85149;">${errorMessage}</p>
            <button class="btn btn-secondary" onclick="loadUserCases()">
                <i class="fas fa-redo"></i> 重试
            </button>
        </div>
    `;
}

// 创建案例元素
function createCaseElement(case_) {
    const div = document.createElement('div');
    div.className = 'issue-item';
    div.setAttribute('data-case-id', case_.id);
    div.onclick = () => selectCase(case_);
    
    const priorityClass = `priority-${case_.priority}`;
    const statusClass = `status-${case_.status}`;
    
    div.innerHTML = `
        <div class="issue-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="issue-content">
            <div class="issue-title">${case_.case_id}</div>
            <div class="issue-meta">
                <span class="priority-badge ${priorityClass}">${case_.priority}</span>
                <span class="status-badge ${statusClass}">${case_.status}</span>
                <span class="assignee">负责人: ${case_.assignee || '未分配'}</span>
            </div>
            <div class="issue-description">
                Product: ${case_.product_id} | Lot: ${case_.lot_id} | Tool: ${case_.tool}
            </div>
        </div>
    `;
    
    return div;
}

// 选择Case
function selectCase(case_) {
    // 移除之前选中的Case
    document.querySelectorAll('.issue-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 选中当前Case
    const caseElement = document.querySelector(`[data-case-id="${case_.id}"]`);
    if (caseElement) {
        caseElement.classList.add('active');
    }
    
    selectedCase = case_;
    
    // 更新右侧面板标题
    const panelTitle = document.getElementById('panelTitle');
    if (panelTitle) {
        panelTitle.textContent = `Case #${case_.case_id}`;
    }
    
    // 显示Case信息面板，但保持工作流信息可访问
    const caseInfoPanel = document.getElementById('caseInfoPanel');
    const stepsContainer = document.getElementById('stepsContainer');
    const caseInfoSection = document.getElementById('caseInfoSection');
    
    if (caseInfoPanel) {
        caseInfoPanel.style.display = 'block';
        showCaseInfoPanel(case_);
    }
    
    // 不完全隐藏工作流信息，而是创建标签切换
    createPanelTabs();
    
    // 默认显示Case信息标签
    showPanelTab('case-info');
}

// 创建面板标签
function createPanelTabs() {
    const rightPanel = document.getElementById('rightPanel');
    const panelTitle = document.getElementById('panelTitle');
    
    // 检查是否已经存在标签
    let tabsContainer = document.getElementById('panelTabs');
    if (!tabsContainer) {
        tabsContainer = document.createElement('div');
        tabsContainer.id = 'panelTabs';
        tabsContainer.className = 'panel-tabs';
        
        tabsContainer.innerHTML = `
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="case-info" onclick="showPanelTab('case-info')">
                    <i class="fas fa-info-circle"></i> Case信息
                </button>
                <button class="tab-button" data-tab="workflow" onclick="showPanelTab('workflow')">
                    <i class="fas fa-project-diagram"></i> 工作流
                </button>
            </div>
        `;
        
        // 插入到面板标题后面
        const panelHeader = rightPanel.querySelector('.panel-header');
        if (panelHeader) {
            panelHeader.appendChild(tabsContainer);
        }
    }
}

// 显示指定的面板标签
function showPanelTab(tabName) {
    // 更新标签按钮状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
        if (button.getAttribute('data-tab') === tabName) {
            button.classList.add('active');
        }
    });
    
    // 显示/隐藏相应的内容
    const caseInfoPanel = document.getElementById('caseInfoPanel');
    const stepsContainer = document.getElementById('stepsContainer');
    const caseInfoSection = document.getElementById('caseInfoSection');
    
    if (tabName === 'case-info') {
        // 显示Case信息
        if (caseInfoPanel) caseInfoPanel.style.display = 'block';
        if (stepsContainer) stepsContainer.style.display = 'none';
        if (caseInfoSection) caseInfoSection.style.display = 'none';
    } else if (tabName === 'workflow') {
        // 显示工作流信息
        if (caseInfoPanel) caseInfoPanel.style.display = 'none';
        if (stepsContainer) stepsContainer.style.display = 'block';
        if (caseInfoSection) caseInfoSection.style.display = 'none';
    }
}

// 移除面板标签
function removePanelTabs() {
    const tabsContainer = document.getElementById('panelTabs');
    if (tabsContainer) {
        tabsContainer.remove();
    }
}

// 显示Case信息面板
function showCaseInfoPanel(case_) {
    const caseInfoPanel = document.getElementById('caseInfoPanel');
    const caseInfoSection = document.getElementById('caseInfoSection');
    const stepsContainer = document.getElementById('stepsContainer');
    
    // 隐藏其他区域
    caseInfoSection.style.display = 'none';
    stepsContainer.style.display = 'none';
    
    // 显示Case信息面板
    caseInfoPanel.style.display = 'block';
    
    // 更新面板标题
    const panelTitle = document.getElementById('panelTitle');
    panelTitle.textContent = `Case #${case_.id}`;
    
    // 更新分配者信息
    updateAssignees(case_);
    
    // 更新标签
    updateLabels(case_);
    
    // 更新其他信息
    updateCaseDetails(case_);
}

// 更新分配者信息
function updateAssignees(case_) {
    const assigneesContent = document.getElementById('assigneesContent');
    
    if (case_.assignee && case_.assignee !== 'Unassigned') {
        assigneesContent.innerHTML = `
            <div class="assignee-item">
                <img src="https://github.com/identicons/${case_.assignee}.png" alt="Avatar" class="avatar">
                <span>${case_.assignee}</span>
            </div>
        `;
    } else {
        assigneesContent.innerHTML = '<div class="empty-state">No assignees</div>';
    }
}

// 更新标签
function updateLabels(case_) {
    const labelsContent = document.getElementById('labelsContent');
    
    if (case_.labels && case_.labels.length > 0) {
        labelsContent.innerHTML = case_.labels.map(label => 
            `<span class="label label-${label.color}">${label.name}</span>`
        ).join('');
    } else {
        labelsContent.innerHTML = '<div class="empty-state">No labels</div>';
    }
}

// 更新Case详细信息
function updateCaseDetails(case_) {
    // 更新项目信息
    const projectsContent = document.getElementById('projectsContent');
    projectsContent.innerHTML = case_.project ? 
        `<div class="project-item">${case_.project}</div>` : 
        '<div class="empty-state">No projects</div>';
    
    // 更新里程碑
    const milestoneContent = document.getElementById('milestoneContent');
    milestoneContent.innerHTML = case_.milestone ? 
        `<div class="milestone-item">${case_.milestone}</div>` : 
        '<div class="empty-state">No milestone</div>';
    
    // 更新关系
    const relationshipsContent = document.getElementById('relationshipsContent');
    relationshipsContent.innerHTML = '<div class="empty-state">None yet</div>';
    
    // 更新参与者
    const participantsContent = document.getElementById('participantsContent');
    const participants = case_.assignee ? [case_.assignee] : [];
    if (participants.length > 0) {
        participantsContent.innerHTML = `
            <div class="participants-list">
                ${participants.map(p => 
                    `<img src="https://github.com/identicons/${p}.png" alt="${p}" class="avatar" title="${p}">`
                ).join('')}
            </div>
        `;
    } else {
        participantsContent.innerHTML = '<div class="empty-state">No participants</div>';
    }
}

// Case信息面板配置功能
function configureAssignees() {
    // TODO: 实现分配者配置功能
    console.log('Configure assignees');
    alert('分配者配置功能待实现');
}

function configureLabels() {
    // TODO: 实现标签配置功能
    console.log('Configure labels');
    alert('标签配置功能待实现');
}

function configureProjects() {
    // TODO: 实现项目配置功能
    console.log('Configure projects');
    alert('项目配置功能待实现');
}

function configureMilestone() {
    // TODO: 实现里程碑配置功能
    console.log('Configure milestone');
    alert('里程碑配置功能待实现');
}

function configureRelationships() {
    // TODO: 实现关系配置功能
    console.log('Configure relationships');
    alert('关系配置功能待实现');
}

function configureDevelopment() {
    // TODO: 实现开发配置功能
    console.log('Configure development');
    alert('开发配置功能待实现');
}

function customizeNotifications() {
    // TODO: 实现通知自定义功能
    console.log('Customize notifications');
    alert('通知自定义功能待实现');
}

function toggleNotifications() {
    const notificationBtn = document.querySelector('.notification-btn');
    const icon = notificationBtn.querySelector('i');
    const span = notificationBtn.querySelector('span');
    
    if (icon.classList.contains('fa-bell-slash')) {
        icon.classList.remove('fa-bell-slash');
        icon.classList.add('fa-bell');
        span.textContent = 'Subscribe';
    } else {
        icon.classList.remove('fa-bell');
        icon.classList.add('fa-bell-slash');
        span.textContent = 'Unsubscribe';
    }
}

function createBranch() {
    // TODO: 实现分支创建功能
    console.log('Create branch');
    alert('分支创建功能待实现');
}

function transferIssue() {
    // TODO: 实现Issue转移功能
    console.log('Transfer issue');
    alert('Issue转移功能待实现');
}

function lockConversation() {
    // TODO: 实现对话锁定功能
    console.log('Lock conversation');
    alert('对话锁定功能待实现');
}

function pinIssue() {
    // TODO: 实现Issue固定功能
    console.log('Pin issue');
    alert('Issue固定功能待实现');
}

// 更新案例信息
function updateCaseInfo(case_) {
    const caseTitle = document.getElementById('caseTitle');
    const assigneeName = document.getElementById('assigneeName');
    const caseStatus = document.getElementById('caseStatus');
    
    if (caseTitle) caseTitle.textContent = case_.case_id;
    if (assigneeName) assigneeName.textContent = case_.assignee || '未分配';
    if (caseStatus) caseStatus.textContent = case_.status;
}

// 加载工作流
async function loadWorkflows() {
    try {
        const response = await fetch('/api/workflows');
        if (response.ok) {
            const data = await response.json();
            console.log('Loaded workflows:', data.workflows);
            
            // 更新工作流选择器选项
            const workflowSelect = document.getElementById('workflowSelect');
            if (workflowSelect && data.workflows.length > 0) {
                workflowSelect.innerHTML = '';
                data.workflows.forEach(workflow => {
                    const option = document.createElement('option');
                    option.value = workflow.id;
                    option.textContent = workflow.name;
                    workflowSelect.appendChild(option);
                });
                
                // 设置默认选中第一个工作流
                workflowSelect.value = data.workflows[0].id;
            }
            
            // 加载默认工作流（生产工作流）
            loadWorkflow('dn-production');
        } else {
            console.error('加载工作流失败');
        }
    } catch (error) {
        console.error('加载工作流错误:', error);
    }
}

// 加载特定工作流
async function loadWorkflow(workflowId = 'dn-production') {
    try {
        console.log('Loading workflow:', workflowId);
        const response = await fetch(`/api/workflows/${workflowId}`);
        if (response.ok) {
            const workflow = await response.json();
            console.log('Loaded workflow:', workflow.name);
            console.log('User module:', currentUser.module);
            console.log('Workflow config:', workflow.config);
            console.log('Visible sections:', workflow.config.sections?.length || 0);
            
            if (workflow.config && workflow.config.sections) {
                workflow.config.sections.forEach((section, index) => {
                    console.log(`Section ${index}:`, section.title, 'Steps:', section.steps?.length || 0);
                    if (section.steps) {
                        section.steps.forEach((step, stepIndex) => {
                            console.log(`  Step ${stepIndex}:`, step.title, 'Permissions:', step.permissions);
                        });
                    }
                });
            }
            
            renderWorkflowDiagram(workflow);
        } else {
            console.error('加载工作流失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('加载工作流错误:', error);
    }
}

// 渲染工作流图
function renderWorkflowDiagram(workflow) {
    console.log('=== renderWorkflowDiagram START ===');
    console.log('Workflow:', workflow);
    
    const diagramContainer = document.getElementById('workflowDiagram');
    if (!diagramContainer) {
        console.error('Workflow diagram container not found');
        return;
    }
    
    diagramContainer.innerHTML = '';
    
    const config = workflow.config;
    console.log('Config:', config);
    
    if (!config) {
        console.warn('No workflow config found');
        diagramContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-project-diagram"></i>
                <p>暂无工作流配置</p>
            </div>
        `;
        return;
    }
    
    // 支持新的节点+连接结构
    if (config.nodes && config.connections) {
        renderNodeBasedWorkflow(config, diagramContainer);
    } else if (config.sections) {
        renderSectionBasedWorkflow(config, diagramContainer);
    } else {
        console.warn('Unknown workflow config structure');
        diagramContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-project-diagram"></i>
                <p>不支持的工作流配置格式</p>
            </div>
        `;
    }
    
    console.log('=== renderWorkflowDiagram END ===');
}

// 渲染基于节点的工作流
function renderNodeBasedWorkflow(config, container) {
    console.log('Rendering node-based workflow');
    
    // 创建SVG容器用于绘制连接线
    const svgContainer = document.createElement('div');
    svgContainer.className = 'workflow-svg-container';
    svgContainer.style.cssText = `
        position: relative;
        width: 100%;
        height: 100%;
        min-height: 800px;
    `;
    
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        pointer-events: none;
    `;
    
    const nodesContainer = document.createElement('div');
    nodesContainer.className = 'workflow-nodes-container';
    nodesContainer.style.cssText = `
        position: relative;
        width: 100%;
        height: 100%;
        z-index: 2;
    `;
    
    // 过滤可见节点
    const visibleNodes = config.nodes.filter(node => {
        const userModule = currentUser.module;
        const nodePermissions = node.permissions || ['ye', 'admin'];
        return nodePermissions.includes(userModule) || userModule === 'admin';
    });
    
    console.log(`Found ${visibleNodes.length} visible nodes`);
    
    // 渲染节点
    visibleNodes.forEach(node => {
        const nodeElement = createWorkflowNodeElement(node);
        nodesContainer.appendChild(nodeElement);
    });
    
    svgContainer.appendChild(svg);
    svgContainer.appendChild(nodesContainer);
    container.appendChild(svgContainer);
    
    // 渲染连接线
    setTimeout(() => {
        renderConnections(config.connections, visibleNodes, svg);
    }, 100);
}

// 创建工作流节点元素
function createWorkflowNodeElement(node) {
    const nodeDiv = document.createElement('div');
    nodeDiv.className = 'workflow-node';
    nodeDiv.id = `node-${node.id}`;
    
    // 设置节点位置
    nodeDiv.style.cssText = `
        position: absolute;
        left: ${node.position.x}px;
        top: ${node.position.y}px;
        width: 120px;
        height: 100px;
        border: 2px solid #30363d;
        border-radius: 8px;
        background: #21262d;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px;
        text-align: center;
    `;
    
    // 根据状态设置不同的样式
    let statusColor = '#30363d';
    let statusText = '未知';
    
    switch(node.status) {
        case 'completed':
            statusColor = '#238636';
            statusText = '已完成';
            break;
        case 'running':
            statusColor = '#f85149';
            statusText = '进行中';
            break;
        case 'pending':
            statusColor = '#ff8c00';
            statusText = '待处理';
            break;
        case 'failed':
            statusColor = '#da3633';
            statusText = '失败';
            break;
        case 'disabled':
            statusColor = '#6e7681';
            statusText = '禁用';
            break;
    }
    
    nodeDiv.style.borderColor = statusColor;
    
    // 添加悬停效果
    nodeDiv.addEventListener('mouseenter', () => {
        nodeDiv.style.borderColor = '#1f6feb';
        nodeDiv.style.transform = 'scale(1.05)';
        nodeDiv.style.boxShadow = `0 0 20px ${statusColor}40`;
    });
    
    nodeDiv.addEventListener('mouseleave', () => {
        nodeDiv.style.borderColor = statusColor;
        nodeDiv.style.transform = 'scale(1)';
        nodeDiv.style.boxShadow = 'none';
    });
    
    // 添加点击事件
    nodeDiv.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        selectWorkflowNode(node, nodeDiv);
    });
    
    nodeDiv.innerHTML = `
        <div class="node-icon" style="font-size: 24px; color: ${statusColor}; margin-bottom: 4px;">
            ${node.icon}
        </div>
        <div class="node-title" style="font-size: 12px; color: #f0f6fc; font-weight: 600; margin-bottom: 4px;">
            ${node.title}
        </div>
        <div class="node-status" style="font-size: 10px; color: ${statusColor};">
            ${statusText}
        </div>
    `;
    
    return nodeDiv;
}

// 渲染连接线
function renderConnections(connections, visibleNodes, svg) {
    console.log('Rendering connections:', connections);
    
    connections.forEach(conn => {
        const fromNode = visibleNodes.find(n => n.id === conn.from);
        const toNode = visibleNodes.find(n => n.id === conn.to);
        
        if (!fromNode || !toNode) {
            console.log(`Skipping connection ${conn.from} -> ${conn.to} (node not visible)`);
            return;
        }
        
        const fromElement = document.getElementById(`node-${conn.from}`);
        const toElement = document.getElementById(`node-${conn.to}`);
        
        if (!fromElement || !toElement) {
            console.log(`Skipping connection ${conn.from} -> ${conn.to} (element not found)`);
            return;
        }
        
        // 计算连接线的位置
        const fromRect = fromElement.getBoundingClientRect();
        const toRect = toElement.getBoundingClientRect();
        const svgRect = svg.getBoundingClientRect();
        
        const fromX = fromRect.left - svgRect.left + fromRect.width / 2;
        const fromY = fromRect.top - svgRect.top + fromRect.height / 2;
        const toX = toRect.left - svgRect.left + toRect.width / 2;
        const toY = toRect.top - svgRect.top + toRect.height / 2;
        
        // 创建连接线
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', fromX);
        line.setAttribute('y1', fromY);
        line.setAttribute('x2', toX);
        line.setAttribute('y2', toY);
        line.setAttribute('stroke', conn.status === 'active' ? '#1f6feb' : '#30363d');
        line.setAttribute('stroke-width', '2');
        line.setAttribute('stroke-dasharray', conn.status === 'active' ? '0' : '5,5');
        
        // 添加箭头
        const arrowId = `arrow-${conn.from}-${conn.to}`;
        const defs = svg.querySelector('defs') || svg.appendChild(document.createElementNS('http://www.w3.org/2000/svg', 'defs'));
        
        const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        marker.setAttribute('id', arrowId);
        marker.setAttribute('viewBox', '0 0 10 10');
        marker.setAttribute('refX', '9');
        marker.setAttribute('refY', '3');
        marker.setAttribute('markerWidth', '6');
        marker.setAttribute('markerHeight', '6');
        marker.setAttribute('orient', 'auto');
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', 'M0,0 L0,6 L9,3 z');
        path.setAttribute('fill', conn.status === 'active' ? '#1f6feb' : '#30363d');
        
        marker.appendChild(path);
        defs.appendChild(marker);
        
        line.setAttribute('marker-end', `url(#${arrowId})`);
        
        svg.appendChild(line);
    });
}

// 选择工作流节点
function selectWorkflowNode(node, element) {
    // 移除之前选中的节点
    document.querySelectorAll('.workflow-node').forEach(n => {
        n.classList.remove('selected');
    });
    
    // 选中当前节点
    element.classList.add('selected');
    
    // 清除Case选择状态
    document.querySelectorAll('.issue-item').forEach(item => {
        item.classList.remove('active');
    });
    selectedCase = null;
    
    // 移除面板标签，恢复原始工作流模式
    removePanelTabs();
    
    // 更新右侧面板标题
    const panelTitle = document.getElementById('panelTitle');
    if (panelTitle) {
        panelTitle.textContent = node.name || '工作流节点';
    }
    
    // 隐藏Case信息面板，显示工作流节点详情
    const caseInfoPanel = document.getElementById('caseInfoPanel');
    const stepsContainer = document.getElementById('stepsContainer');
    const caseInfoSection = document.getElementById('caseInfoSection');
    
    if (caseInfoPanel) {
        caseInfoPanel.style.display = 'none';
    }
    
    if (stepsContainer) {
        stepsContainer.style.display = 'block';
    }
    
    if (caseInfoSection) {
        caseInfoSection.style.display = 'none';
    }
    
    // 更新节点详情
    updateNodeDetails(node);
}

// 更新节点详情
function updateNodeDetails(node) {
    console.log('Updating node details for:', node);
    
    const stepsContainer = document.getElementById('stepsContainer');
    if (!stepsContainer) return;
    
    // 清空现有内容
    stepsContainer.innerHTML = '';
    
    // 创建节点详情面板
    const nodePanel = document.createElement('div');
    nodePanel.className = 'node-panel active';
    
    // 根据节点状态设置不同的操作界面
    let actionButtons = '';
    let statusClass = node.status || 'pending';
    
    if (node.status === 'completed') {
        actionButtons = `
            <div class="action-buttons">
                <button class="btn btn-secondary" onclick="viewNodeResults('${node.id}')">
                    <i class="fas fa-eye"></i> 查看结果
                </button>
                <button class="btn btn-secondary" onclick="downloadNodeData('${node.id}')">
                    <i class="fas fa-download"></i> 下载数据
                </button>
            </div>
        `;
    } else if (node.status === 'running') {
        actionButtons = `
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="executeNode('${node.id}')">
                    <i class="fas fa-play"></i> 执行节点
                </button>
                <button class="btn btn-secondary" onclick="configureNode('${node.id}')">
                    <i class="fas fa-cog"></i> 配置参数
                </button>
                <button class="btn btn-warning" onclick="pauseNode('${node.id}')">
                    <i class="fas fa-pause"></i> 暂停
                </button>
            </div>
        `;
    } else if (node.status === 'pending') {
        actionButtons = `
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="startNode('${node.id}')">
                    <i class="fas fa-play"></i> 开始执行
                </button>
                <button class="btn btn-secondary" onclick="configureNode('${node.id}')">
                    <i class="fas fa-cog"></i> 配置参数
                </button>
            </div>
        `;
    } else if (node.status === 'failed') {
        actionButtons = `
            <div class="action-buttons">
                <button class="btn btn-warning" onclick="retryNode('${node.id}')">
                    <i class="fas fa-redo"></i> 重试
                </button>
                <button class="btn btn-secondary" onclick="viewNodeLogs('${node.id}')">
                    <i class="fas fa-file-alt"></i> 查看日志
                </button>
                <button class="btn btn-secondary" onclick="skipNode('${node.id}')">
                    <i class="fas fa-forward"></i> 跳过
                </button>
            </div>
        `;
    } else {
        actionButtons = `
            <div class="action-buttons">
                <button class="btn btn-secondary" disabled>
                    <i class="fas fa-lock"></i> 暂不可操作
                </button>
            </div>
        `;
    }
    
    nodePanel.innerHTML = `
        <div class="node-header">
            <div class="node-title-section">
                <div class="node-icon-large">
                    ${node.icon}
                </div>
                <div class="node-info">
                    <div class="node-name">${node.title}</div>
                    <div class="node-type">${node.type || 'unknown'}</div>
                </div>
            </div>
            <div class="node-status ${statusClass}">${getNodeStatusText(node.status)}</div>
        </div>
        <div class="node-content">
            <div class="node-description">
                <h4>节点描述</h4>
                <p>${node.description || '暂无描述'}</p>
            </div>
            
            ${node.config ? `
            <div class="node-config-section">
                <h4>配置信息</h4>
                <div class="config-grid">
                    ${node.config.function ? `
                    <div class="config-item">
                        <label>功能</label>
                        <span>${node.config.function}</span>
                    </div>
                    ` : ''}
                    ${node.config.requiredParams ? `
                    <div class="config-item">
                        <label>必选参数</label>
                        <span>${node.config.requiredParams.join(', ')}</span>
                    </div>
                    ` : ''}
                    ${node.config.optionalParams ? `
                    <div class="config-item">
                        <label>可选参数</label>
                        <span>${node.config.optionalParams.join(', ')}</span>
                    </div>
                    ` : ''}
                    ${node.config.userInteraction ? `
                    <div class="config-item">
                        <label>用户交互</label>
                        <span>${node.config.userInteraction}</span>
                    </div>
                    ` : ''}
                    ${node.config.moduleAccess ? `
                    <div class="config-item">
                        <label>模块访问</label>
                        <span>${node.config.moduleAccess}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}
            
            <div class="node-io-section">
                <div class="io-item">
                    <label>输入</label>
                    <span>${node.inputs && node.inputs.length > 0 ? node.inputs.join(', ') : '无'}</span>
                </div>
                <div class="io-item">
                    <label>输出</label>
                    <span>${node.outputs && node.outputs.length > 0 ? node.outputs.join(', ') : '无'}</span>
                </div>
                <div class="io-item">
                    <label>权限</label>
                    <span>${node.permissions ? node.permissions.join(', ') : '无限制'}</span>
                </div>
            </div>
            
            ${actionButtons}
        </div>
    `;
    
    stepsContainer.appendChild(nodePanel);
}

// 获取节点状态文本
function getNodeStatusText(status) {
    switch(status) {
        case 'completed': return '已完成';
        case 'running': return '进行中';
        case 'pending': return '待处理';
        case 'failed': return '失败';
        case 'disabled': return '禁用';
        default: return '未知';
    }
}

// 更新图表
function updateDashboardCharts(stats) {
    updateCasesTrendChart(stats.trend_data || []);
    updateToolDistributionChart(stats.tool_breakdown || {});
    updateStatusDistributionChart(stats);
    updateDefectClassChart(stats.defect_class_breakdown || {});
}

// 更新案例趋势图
function updateCasesTrendChart(trendData) {
    const ctx = document.getElementById('casesTrendChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (chartInstances.casesTrendChart) {
        chartInstances.casesTrendChart.destroy();
    }
    
    // 准备数据
    const labels = trendData.map(item => item.date).reverse();
    const data = trendData.map(item => item.count).reverse();
    
    chartInstances.casesTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '案例数量',
                data: data,
                borderColor: '#1f6feb',
                backgroundColor: 'rgba(31, 111, 235, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#30363d'
                    },
                    ticks: {
                        color: '#7d8590'
                    }
                },
                x: {
                    grid: {
                        color: '#30363d'
                    },
                    ticks: {
                        color: '#7d8590'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#f0f6fc'
                    }
                }
            }
        }
    });
}

// 更新工具分布图
function updateToolDistributionChart(toolData) {
    const ctx = document.getElementById('toolDistributionChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (chartInstances.toolDistributionChart) {
        chartInstances.toolDistributionChart.destroy();
    }
    
    const labels = Object.keys(toolData);
    const data = Object.values(toolData);
    
    chartInstances.toolDistributionChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '案例数量',
                data: data,
                backgroundColor: [
                    '#238636',
                    '#1f6feb',
                    '#f85149',
                    '#ff8c00',
                    '#8b5cf6'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#30363d'
                    },
                    ticks: {
                        color: '#7d8590'
                    }
                },
                x: {
                    grid: {
                        color: '#30363d'
                    },
                    ticks: {
                        color: '#7d8590'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#f0f6fc'
                    }
                }
            }
        }
    });
}

// 更新状态分布图
function updateStatusDistributionChart(stats) {
    const ctx = document.getElementById('statusDistributionChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (chartInstances.statusDistributionChart) {
        chartInstances.statusDistributionChart.destroy();
    }
    
    const data = [
        stats.pending_cases || 0,
        stats.in_progress_cases || 0,
        stats.completed_cases || 0
    ];
    
    chartInstances.statusDistributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Pending', 'In Progress', 'Completed'],
            datasets: [{
                data: data,
                backgroundColor: [
                    '#ff8c00',
                    '#1f6feb',
                    '#238636'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#f0f6fc'
                    }
                }
            }
        }
    });
}

// 更新缺陷类别图
function updateDefectClassChart(defectClassData) {
    const ctx = document.getElementById('defectClassChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (chartInstances.defectClassChart) {
        chartInstances.defectClassChart.destroy();
    }
    
    const labels = Object.keys(defectClassData);
    const data = Object.values(defectClassData);
    
    chartInstances.defectClassChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#238636',
                    '#1f6feb',
                    '#f85149',
                    '#ff8c00',
                    '#8b5cf6',
                    '#e91e63',
                    '#00bcd4',
                    '#ff5722'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#f0f6fc'
                    }
                }
            }
        }
    });
}

// Dashboard功能
function refreshDashboard() {
    loadDefectCasesStats();
}

// 查询执行
async function executeQuery() {
    const productId = document.getElementById('productId').value;
    const lotId = document.getElementById('lotId').value;
    const layerId = document.getElementById('layerId').value;
    const tool = document.getElementById('toolSelect').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const defectClass = document.getElementById('defectClass').value;
    const priority = document.getElementById('prioritySelect').value;
    const status = document.getElementById('statusSelect').value;
    
    // 显示加载状态
    updateQueryStatusIndicator(true, 0, false, true);
    
    const params = new URLSearchParams();
    if (productId) params.append('product_id', productId);
    if (lotId) params.append('lot_id', lotId);
    if (layerId) params.append('layer_id', layerId);
    if (tool) params.append('tool', tool);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (defectClass) params.append('defect_class', defectClass);
    if (priority) params.append('priority', priority);
    if (status) params.append('status', status);
    
    params.append('page', currentPageNum);
    params.append('page_size', 10);
    
    try {
        const response = await fetch(`/api/defect-cases?${params}`);
        if (response.ok) {
            const data = await response.json();
            displayQueryResults(data);
            
            // 根据查询结果更新统计数据和图表
            updateDashboardFromQueryResults(data);
        } else {
            console.error('查询失败');
            updateQueryStatusIndicator(true, 0, true);
        }
    } catch (error) {
        console.error('查询错误:', error);
        updateQueryStatusIndicator(true, 0, true);
    }
}

// 根据查询结果更新Dashboard统计数据和图表
function updateDashboardFromQueryResults(data) {
    if (!data.cases || data.cases.length === 0) {
        // 如果没有查询结果，显示空数据
        const emptyStats = {
            total_cases: 0,
            pending_cases: 0,
            in_progress_cases: 0,
            completed_cases: 0,
            tool_breakdown: {},
            defect_class_breakdown: {},
            trend_data: []
        };
        updateDashboardStats(emptyStats);
        updateDashboardCharts(emptyStats);
        updateQueryStatusIndicator(true, 0, true);
        return;
    }
    
    // 从查询结果计算统计数据
    const stats = calculateStatsFromResults(data.cases);
    
    // 更新统计卡片
    updateDashboardStats(stats);
    
    // 更新图表
    updateDashboardCharts(stats);
    
    // 更新查询状态指示器
    updateQueryStatusIndicator(true, stats.total_cases, false);
    
    console.log('Dashboard updated with query results:', stats);
}

// 更新查询状态指示器
function updateQueryStatusIndicator(isFiltered, resultCount, isEmpty = false, showLoading = false) {
    const indicator = document.getElementById('queryStatusIndicator');
    const statusText = document.getElementById('queryStatusText');
    const resetBtn = document.getElementById('resetQueryBtn');
    
    if (!indicator || !statusText || !resetBtn) return;
    
    if (showLoading) {
        indicator.classList.add('filtered');
        statusText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在查询...';
        statusText.style.color = '#f0f6fc';
        resetBtn.style.display = 'none';
    } else if (isFiltered) {
        indicator.classList.add('filtered');
        if (isEmpty) {
            statusText.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 查询无结果，请调整查询条件';
            statusText.style.color = '#fd7e14';
        } else {
            statusText.textContent = `显示查询结果 (${resultCount} 条记录)`;
            statusText.style.color = '#f0f6fc';
        }
        resetBtn.style.display = 'inline-flex';
    } else {
        indicator.classList.remove('filtered');
        statusText.textContent = '显示全部数据';
        statusText.style.color = '#f0f6fc';
        resetBtn.style.display = 'none';
    }
}

// 从查询结果计算统计数据
function calculateStatsFromResults(cases) {
    const stats = {
        total_cases: cases.length,
        pending_cases: 0,
        in_progress_cases: 0,
        completed_cases: 0,
        tool_breakdown: {},
        defect_class_breakdown: {},
        trend_data: []
    };
    
    // 计算状态分布
    cases.forEach(case_ => {
        switch(case_.status) {
            case 'open':
                stats.pending_cases++;
                break;
            case 'in-progress':
                stats.in_progress_cases++;
                break;
            case 'closed':
                stats.completed_cases++;
                break;
        }
        
        // 计算工具分布
        if (case_.tool) {
            stats.tool_breakdown[case_.tool] = (stats.tool_breakdown[case_.tool] || 0) + 1;
        }
        
        // 计算缺陷类别分布
        if (case_.defect_class) {
            stats.defect_class_breakdown[case_.defect_class] = (stats.defect_class_breakdown[case_.defect_class] || 0) + 1;
        }
    });
    
    // 计算趋势数据（按日期分组）
    const trendMap = {};
    cases.forEach(case_ => {
        if (case_.created_time) {
            const date = case_.created_time.split(' ')[0]; // 提取日期部分
            trendMap[date] = (trendMap[date] || 0) + 1;
        }
    });
    
    // 转换为趋势数据格式
    stats.trend_data = Object.entries(trendMap).map(([date, count]) => ({
        date,
        count
    })).sort((a, b) => new Date(a.date) - new Date(b.date));
    
    return stats;
}

// 显示查询结果
function displayQueryResults(data) {
    const resultsSection = document.getElementById('resultsSection');
    const resultsTableBody = document.getElementById('resultsTableBody');
    const resultsCount = document.getElementById('resultsCount');
    
    if (!resultsSection || !resultsTableBody || !resultsCount) return;
    
    // 显示结果区域
    resultsSection.classList.add('show');
    
    // 更新计数
    resultsCount.textContent = data.total_count;
    
    // 更新分页信息
    totalPages = data.total_pages;
    currentPageNum = data.page;
    updatePagination();
    
    // 清空表格
    resultsTableBody.innerHTML = '';
    
    // 填充数据
    data.cases.forEach(case_ => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${case_.case_id}</td>
            <td>${case_.product_id}</td>
            <td>${case_.lot_id}</td>
            <td>${case_.layer_id}</td>
            <td>${case_.tool}</td>
            <td>${case_.defect_class || '-'}</td>
            <td><span class="priority-badge priority-${case_.priority}">${case_.priority}</span></td>
            <td><span class="status-badge status-${case_.status}">${case_.status}</span></td>
            <td>${case_.assignee || '未分配'}</td>
            <td>${formatDate(case_.created_time)}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="viewCaseDetail('${case_.case_id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-secondary" onclick="editCase('${case_.case_id}')">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        resultsTableBody.appendChild(row);
    });
}

// 更新分页信息
function updatePagination() {
    const currentPageNumElement = document.getElementById('currentPageNum');
    const totalPagesNumElement = document.getElementById('totalPagesNum');
    
    if (currentPageNumElement) currentPageNumElement.textContent = currentPageNum;
    if (totalPagesNumElement) totalPagesNumElement.textContent = totalPages;
}

// 分页功能
function previousPage() {
    if (currentPageNum > 1) {
        currentPageNum--;
        executeQuery();
    }
}

function nextPage() {
    if (currentPageNum < totalPages) {
        currentPageNum++;
        executeQuery();
    }
}

// 清空查询条件
function clearQuery() {
    document.getElementById('productId').value = '';
    document.getElementById('lotId').value = '';
    document.getElementById('layerId').value = '';
    document.getElementById('toolSelect').value = '';
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    document.getElementById('defectClass').value = '';
    document.getElementById('prioritySelect').value = '';
    document.getElementById('statusSelect').value = '';
    
    // 隐藏结果区域
    const resultsSection = document.getElementById('resultsSection');
    if (resultsSection) {
        resultsSection.classList.remove('show');
    }
    
    // 重置分页
    currentPageNum = 1;
    
    // 恢复原始统计数据
    loadDefectCasesStats();
}

// 导出结果
function exportResults() {
    // 实现CSV导出功能
    console.log('导出功能');
}

// 查看案例详情
function viewCaseDetail(caseId) {
    console.log('查看案例详情:', caseId);
}

// 编辑案例
function editCase(caseId) {
    console.log('编辑案例:', caseId);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 其他功能
function createNewIssue() {
    console.log('创建新Issue');
}

function refreshIssues() {
    if (currentPage === 'cases') {
        loadUserCases();
    }
}

// 切换右侧面板
function toggleRightPanel() {
    const rightPanel = document.getElementById('rightPanel');
    const casesLayout = document.getElementById('casesLayout');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (rightPanel && casesLayout && toggleIcon) {
        const isCollapsed = rightPanel.classList.contains('collapsed');
        
        if (isCollapsed) {
            // 展开面板
            rightPanel.classList.remove('collapsed');
            casesLayout.classList.remove('right-collapsed');
            toggleIcon.classList.remove('fa-chevron-left');
            toggleIcon.classList.add('fa-chevron-right');
            localStorage.setItem('rightPanelCollapsed', 'false');
        } else {
            // 折叠面板
            rightPanel.classList.add('collapsed');
            casesLayout.classList.add('right-collapsed');
            toggleIcon.classList.remove('fa-chevron-right');
            toggleIcon.classList.add('fa-chevron-left');
            localStorage.setItem('rightPanelCollapsed', 'true');
        }
    }
}

// 处理工作流选择变化
function handleWorkflowChange() {
    const workflowSelect = document.getElementById('workflowSelect');
    if (workflowSelect) {
        const selectedWorkflow = workflowSelect.value;
        console.log('Switching to workflow:', selectedWorkflow);
        loadWorkflow(selectedWorkflow);
    }
}

// 处理时间范围变化
function handleTimeChange() {
    const timeRange = document.getElementById('timeRange');
    if (timeRange) {
        const selectedTime = timeRange.value;
        console.log('Time range changed to:', selectedTime);
        // 这里可以添加时间范围变化的处理逻辑
    }
}

// 测试工作流图容器
function testWorkflowContainer() {
    const container = document.getElementById('workflowDiagram');
    console.log('Workflow container test:', container);
    if (container) {
        console.log('Container found, setting test content...');
        container.innerHTML = `
            <div style="padding: 40px; text-align: center; color: #ffffff; border: 3px solid #ff6b6b; border-radius: 12px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-size: 18px; margin: 20px;">
                <h2 style="color: #ffffff; margin-bottom: 20px;">🚀 工作流容器测试</h2>
                <p style="color: #ffffff; font-size: 16px;">如果您看到这个消息，说明工作流容器正常工作！</p>
                <p style="color: #ffffff; font-size: 14px;">正在准备加载真实工作流数据...</p>
            </div>
        `;
        
        // 2秒后尝试加载真实工作流
        setTimeout(() => {
            console.log('Loading real workflow...');
            loadWorkflow('dn-production');
        }, 2000);
    } else {
        console.error('Workflow container not found!');
    }
}

// 执行步骤
function executeStep(stepId) {
    console.log('Executing step:', stepId);
    
    // 模拟执行过程
    const confirmExecute = confirm(`确定要执行步骤 ${stepId} 吗？这将开始处理该步骤。`);
    if (confirmExecute) {
        // 这里可以添加实际的步骤执行逻辑
        alert(`正在执行步骤: ${stepId}\n\n请稍后查看执行结果。`);
        
        // 模拟执行完成后重新加载工作流
        setTimeout(() => {
            loadWorkflow('dn-production');
        }, 1000);
    }
}

// 配置步骤
function configureStep(stepId) {
    console.log('Configuring step:', stepId);
    
    // 打开配置对话框
    const config = prompt(`请输入步骤 ${stepId} 的配置参数（JSON格式）:`, '{"param1": "value1", "param2": "value2"}');
    if (config) {
        try {
            const parsedConfig = JSON.parse(config);
            console.log('Step configuration:', parsedConfig);
            alert(`步骤 ${stepId} 配置已保存！`);
        } catch (e) {
            alert('配置格式错误，请输入有效的JSON格式！');
        }
    }
}

// 跳过步骤
function skipStep(stepId) {
    console.log('Skipping step:', stepId);
    
    const confirmSkip = confirm(`确定要跳过步骤 ${stepId} 吗？跳过后该步骤将被标记为已完成。`);
    if (confirmSkip) {
        alert(`步骤 ${stepId} 已跳过！`);
        
        // 模拟跳过后重新加载工作流
        setTimeout(() => {
            loadWorkflow('dn-production');
        }, 1000);
    }
}

// 查看步骤详情
function viewStepDetails(stepId) {
    console.log('Viewing step details:', stepId);
    alert(`查看步骤 ${stepId} 的详细信息和执行历史。`);
}

// 下载步骤结果
function downloadStepResults(stepId) {
    console.log('Downloading step results:', stepId);
    alert(`下载步骤 ${stepId} 的执行结果文件。`);
}

// 渲染基于section的工作流（向后兼容）
function renderSectionBasedWorkflow(config, container) {
    console.log('Rendering section-based workflow');
    
    // 创建工作流容器
    const flowContainer = document.createElement('div');
    flowContainer.className = 'workflow-flow';
    
    config.sections.forEach((section, sectionIndex) => {
        console.log(`Processing section ${sectionIndex}:`, section);
        
        if (!section.steps || section.steps.length === 0) {
            console.warn(`Section ${sectionIndex} has no steps`);
            return;
        }
        
        // 过滤可见步骤
        const visibleSteps = section.steps.filter(step => {
            const userModule = currentUser.module;
            const stepPermissions = step.permissions || ['ye', 'admin'];
            return stepPermissions.includes(userModule) || userModule === 'admin';
        });
        
        if (visibleSteps.length === 0) {
            console.log(`Section ${section.title} has no visible steps`);
            return;
        }
        
        const sectionDiv = document.createElement('div');
        sectionDiv.className = 'flow-section';
        
        // 添加标题
        const titleDiv = document.createElement('div');
        titleDiv.className = 'section-title';
        titleDiv.textContent = section.title;
        sectionDiv.appendChild(titleDiv);
        
        // 添加步骤
        visibleSteps.forEach(step => {
            const stepDiv = createLegacyWorkflowNode(step);
            sectionDiv.appendChild(stepDiv);
        });
        
        flowContainer.appendChild(sectionDiv);
    });
    
    container.appendChild(flowContainer);
}

// 创建传统工作流节点
function createLegacyWorkflowNode(step) {
    const stepDiv = document.createElement('div');
    stepDiv.className = 'flow-node';
    
    stepDiv.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        selectLegacyStep(step, this);
    });
    
    // 根据步骤状态设置样式
    if (step.status === 'completed') {
        stepDiv.classList.add('completed');
    } else if (step.status === 'running') {
        stepDiv.classList.add('running');
    } else if (step.status === 'disabled') {
        stepDiv.classList.add('disabled');
    } else {
        stepDiv.classList.add('pending');
    }
    
    stepDiv.innerHTML = `
        <div class="node-icon">${step.icon || '⚙️'}</div>
        <div class="node-title">${step.title}</div>
        <div class="node-status">${getLegacyStatusText(step.status)}</div>
    `;
    
    return stepDiv;
}

// 选择传统工作流步骤
function selectLegacyStep(step, element) {
    console.log('Selected legacy step:', step);
    
    // 高亮选中的节点
    document.querySelectorAll('.flow-node').forEach(node => {
        node.classList.remove('active');
    });
    
    element.classList.add('active');
    
    // 更新右侧面板
    updateLegacyStepDetails(step);
}

// 更新传统步骤详情
function updateLegacyStepDetails(step) {
    const stepsContainer = document.getElementById('stepsContainer');
    if (!stepsContainer) return;
    
    stepsContainer.innerHTML = '';
    
    const stepPanel = document.createElement('div');
    stepPanel.className = 'step-panel active';
    
    stepPanel.innerHTML = `
        <div class="step-header">
            <div class="step-title">
                <div class="step-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="step-name">${step.title}</div>
            </div>
            <div class="step-status ${step.status || 'pending'}">${getLegacyStatusText(step.status)}</div>
        </div>
        <div class="step-content">
            <div class="input-panel">
                <div class="input-group">
                    <label>步骤描述</label>
                    <textarea readonly class="form-control">${step.description || '暂无描述'}</textarea>
                </div>
                <div class="input-group">
                    <label>权限要求</label>
                    <input type="text" readonly class="form-control" value="${step.permissions ? step.permissions.join(', ') : '无限制'}">
                </div>
            </div>
        </div>
    `;
    
    stepsContainer.appendChild(stepPanel);
}

// 获取传统状态文本
function getLegacyStatusText(status) {
    switch(status) {
        case 'completed': return '已完成';
        case 'running': return '进行中';
        case 'pending': return '待处理';
        case 'disabled': return '禁用';
        default: return '待处理';
    }
}

// 节点操作函数
function executeNode(nodeId) {
    console.log('Executing node:', nodeId);
    alert(`正在执行节点: ${nodeId}\n\n请稍后查看执行结果。`);
}

function configureNode(nodeId) {
    console.log('Configuring node:', nodeId);
    const config = prompt(`请输入节点 ${nodeId} 的配置参数（JSON格式）:`, '{"param1": "value1", "param2": "value2"}');
    if (config) {
        try {
            const parsedConfig = JSON.parse(config);
            console.log('Node configuration:', parsedConfig);
            alert(`节点 ${nodeId} 配置已保存！`);
        } catch (e) {
            alert('配置格式错误，请输入有效的JSON格式！');
        }
    }
}

function pauseNode(nodeId) {
    console.log('Pausing node:', nodeId);
    const confirmPause = confirm(`确定要暂停节点 ${nodeId} 吗？`);
    if (confirmPause) {
        alert(`节点 ${nodeId} 已暂停！`);
    }
}

function startNode(nodeId) {
    console.log('Starting node:', nodeId);
    const confirmStart = confirm(`确定要开始执行节点 ${nodeId} 吗？`);
    if (confirmStart) {
        alert(`节点 ${nodeId} 已开始执行！`);
    }
}

function retryNode(nodeId) {
    console.log('Retrying node:', nodeId);
    const confirmRetry = confirm(`确定要重试节点 ${nodeId} 吗？`);
    if (confirmRetry) {
        alert(`节点 ${nodeId} 正在重试！`);
    }
}

function skipNode(nodeId) {
    console.log('Skipping node:', nodeId);
    const confirmSkip = confirm(`确定要跳过节点 ${nodeId} 吗？跳过后该节点将被标记为已完成。`);
    if (confirmSkip) {
        alert(`节点 ${nodeId} 已跳过！`);
    }
}

function viewNodeResults(nodeId) {
    console.log('Viewing node results:', nodeId);
    alert(`查看节点 ${nodeId} 的执行结果。`);
}

function downloadNodeData(nodeId) {
    console.log('Downloading node data:', nodeId);
    alert(`下载节点 ${nodeId} 的数据文件。`);
}

function viewNodeLogs(nodeId) {
    console.log('Viewing node logs:', nodeId);
    alert(`查看节点 ${nodeId} 的执行日志。`);
}

// 测试页面结构
function testPageStructure() {
    console.log('=== PAGE STRUCTURE TEST ===');
    
    // 检查所有页面元素
    const pages = ['dashboard', 'cases', 'workflow', 'analytics', 'settings'];
    pages.forEach(page => {
        const pageElement = document.getElementById(`${page}-page`);
        console.log(`Page ${page}:`, pageElement ? 'Found' : 'NOT FOUND');
        if (pageElement) {
            console.log(`  - ID: ${pageElement.id}`);
            console.log(`  - Classes: ${pageElement.className}`);
            console.log(`  - Display: ${getComputedStyle(pageElement).display}`);
            console.log(`  - Visibility: ${getComputedStyle(pageElement).visibility}`);
        }
    });
    
    // 检查导航元素
    const navItems = document.querySelectorAll('.nav-item');
    console.log('Navigation items found:', navItems.length);
    navItems.forEach(item => {
        const page = item.getAttribute('data-page');
        console.log(`  - Nav item: ${page}, Active: ${item.classList.contains('active')}`);
    });
    
    // 检查CSS规则
    const pageContentElements = document.querySelectorAll('.page-content');
    console.log('Page content elements found:', pageContentElements.length);
    pageContentElements.forEach(element => {
        const styles = getComputedStyle(element);
        console.log(`  - Element ${element.id}:`);
        console.log(`    Display: ${styles.display}`);
        console.log(`    Visibility: ${styles.visibility}`);
        console.log(`    Opacity: ${styles.opacity}`);
        console.log(`    Has active class: ${element.classList.contains('active')}`);
    });
    
    console.log('=== PAGE STRUCTURE TEST END ===');
}

// 侧边栏折叠/展开功能
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const toggleIcon = document.querySelector('.sidebar-toggle i');
    
    sidebarCollapsed = !sidebarCollapsed;
    
    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        toggleIcon.classList.remove('fa-bars');
        toggleIcon.classList.add('fa-chevron-right');
    } else {
        sidebar.classList.remove('collapsed');
        toggleIcon.classList.remove('fa-chevron-right');
        toggleIcon.classList.add('fa-bars');
    }
    
    // 保存折叠状态到localStorage
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed);
}

// 初始化侧边栏状态
function initSidebarState() {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
        toggleSidebar();
    }
} 