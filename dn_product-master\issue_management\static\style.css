/* CSS Variables */
:root {
    --primary-color: #1f6feb;
    --secondary-color: #7d8590;
    --success-color: #238636;
    --warning-color: #fd7e14;
    --danger-color: #f85149;
    --card-bg: #161b22;
    --border-color: #30363d;
    --text-primary: #f0f6fc;
    --text-secondary: #7d8590;
    --bg-primary: #0d1117;
    --bg-secondary: #21262d;
}

/* 基础布局样式 */
.container {
    min-height: 100vh;
    display: flex;
}

.main-content {
    flex: 1;
    display: block;
}

.page-content {
    display: none; /* 默认隐藏，通过JavaScript控制显示 */
    padding: 20px;
}

.page-content.active {
    display: block;
}

/* 原有样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #0d1117;
    color: #f0f6fc;
    line-height: 1.6;
}

.container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 左侧导航栏 */
.sidebar {
    width: 250px;
    background-color: #161b22;
    border-right: 1px solid #21262d;
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #21262d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    color: #e6edf3;
    font-size: 18px;
    font-weight: 600;
}

.sidebar-header i {
    margin-right: 8px;
    color: #f85149;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    margin: 2px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #7d8590;
}

.nav-item:hover {
    background-color: #21262d;
    color: #e6edf3;
}

.nav-item.active {
    background-color: #1f6feb;
    color: #ffffff;
}

.nav-item i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #21262d;
}

.user-info {
    display: flex;
    align-items: center;
    color: #7d8590;
    position: relative;
}

.user-info i {
    margin-right: 8px;
    font-size: 18px;
}

.user-info #currentUser {
    flex: 1;
    font-size: 14px;
}

.logout-btn {
    background: none;
    border: none;
    color: #7d8590;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    margin-left: 8px;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: #21262d;
    color: #f85149;
}

.logout-btn i {
    font-size: 14px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    overflow-y: auto;
    background-color: #0d1117;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #21262d;
}

.page-header h1 {
    color: #e6edf3;
    font-size: 24px;
    font-weight: 600;
}

.page-header i {
    margin-right: 10px;
    color: #1f6feb;
}

.page-actions {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background-color: #238636;
    color: #ffffff;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #2ea043;
}

.btn-secondary {
    background-color: #21262d;
    color: #f0f6fc;
    border: 1px solid #30363d;
    font-weight: 500;
}

.btn-secondary:hover {
    background-color: #30363d;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 优先级和状态标记样式 */
.priority-badge, .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    display: inline-block;
}

.priority-badge.priority-high {
    background: rgba(248, 81, 73, 0.1);
    color: #f85149;
    border: 1px solid #f85149;
}

.priority-badge.priority-medium {
    background: rgba(253, 126, 20, 0.1);
    color: #fd7e14;
    border: 1px solid #fd7e14;
}

.priority-badge.priority-low {
    background: rgba(35, 134, 54, 0.1);
    color: #238636;
    border: 1px solid #238636;
}

.status-badge.status-open {
    background: rgba(35, 134, 54, 0.1);
    color: #238636;
    border: 1px solid #238636;
}

.status-badge.status-in-progress {
    background: rgba(253, 126, 20, 0.1);
    color: #fd7e14;
    border: 1px solid #fd7e14;
}

.status-badge.status-closed {
    background: rgba(139, 148, 158, 0.1);
    color: #8b949e;
    border: 1px solid #8b949e;
}

.case-link {
    color: #1f6feb;
    text-decoration: none;
    font-weight: 500;
}

.case-link:hover {
    text-decoration: underline;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: #1f6feb;
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
}

.stat-card:nth-child(1) .stat-icon {
    background: rgba(248, 81, 73, 0.1);
    color: #f85149;
}

.stat-card:nth-child(2) .stat-icon {
    background: rgba(251, 133, 0, 0.1);
    color: #fb8500;
}

.stat-card:nth-child(3) .stat-icon {
    background: rgba(31, 111, 235, 0.1);
    color: #1f6feb;
}

.stat-card:nth-child(4) .stat-icon {
    background: rgba(35, 134, 54, 0.1);
    color: #238636;
}

.stat-content h3 {
    font-size: 28px;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0 0 5px 0;
}

.stat-content p {
    color: #7d8590;
    margin: 0;
    font-size: 14px;
}

/* 筛选器区域 */
.filter-section {
    background: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-section h3 {
    color: #f0f6fc;
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.filter-section h3 i {
    margin-right: 8px;
    color: #1f6feb;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    color: #f0f6fc;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

.filter-group .form-control {
    background: #0d1117;
    border: 1px solid #30363d;
    border-radius: 4px;
    padding: 8px 12px;
    color: #f0f6fc;
    font-size: 14px;
}

.filter-group .form-control:focus {
    outline: none;
    border-color: #1f6feb;
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.1);
}

.date-range {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-range span {
    color: #7d8590;
    font-size: 14px;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

/* 图表区域 */
.charts-section {
    margin-bottom: 30px;
}

.chart-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.chart-container {
    background: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 20px;
}

.chart-container h3 {
    color: #f0f6fc;
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.chart-container h3 i {
    margin-right: 8px;
    color: #1f6feb;
}

.chart-placeholder {
    position: relative;
    height: 250px;
}

.chart-placeholder canvas {
    max-height: 100%;
}

/* Cases页面布局 - 改为1:1比例 */
.cases-layout {
    display: grid;
    grid-template-columns: 300px 1fr 1fr;
    gap: 20px;
    height: calc(100vh - 140px);
}

.cases-layout.right-collapsed {
    grid-template-columns: 300px 1fr 40px;
}

.cases-list {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    overflow: hidden;
}

.cases-header {
    padding: 16px;
    border-bottom: 1px solid #21262d;
    background-color: #21262d;
}

.cases-header h3 {
    color: #f0f6fc;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
}

.search-box {
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #e6edf3;
    font-size: 14px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #7d8590;
}

.cases-container {
    height: calc(100% - 100px);
    overflow-y: auto;
}

/* Issue列表样式 */
.issue-item {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    border-bottom: 1px solid #21262d;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.issue-item:hover {
    background-color: #21262d;
}

.issue-item.active {
    background-color: #0d2818;
    border-left: 3px solid #238636;
}

.issue-icon {
    margin-right: 12px;
    margin-top: 2px;
    font-size: 16px;
}

.issue-content {
    flex: 1;
    min-width: 0;
}

.issue-title {
    color: #f0f6fc;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.4;
}

.issue-meta {
    color: #8b949e;
    font-size: 12px;
}

/* 工作流区域 */
.workflow-section {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.workflow-header {
    padding: 16px;
    border-bottom: 1px solid #21262d;
    background-color: #21262d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.workflow-header h3 {
    color: #f0f6fc;
    font-size: 16px;
    font-weight: 600;
}

.workflow-header select {
    padding: 6px 12px;
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-size: 14px;
}

.workflow-header select:focus {
    outline: none;
    border-color: #58a6ff;
    box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2);
}

.workflow-diagram {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 20px;
    min-height: 400px;
    height: calc(100% - 80px);
    overflow-y: auto;
    position: relative;
    z-index: 1;
}

.workflow-flow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 20px;
    min-height: 300px;
}

.flow-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
}

.parallel-group {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    flex-wrap: wrap;
}

.flow-node {
    background: #161b22;
    border: 2px solid #30363d;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    min-width: 140px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    margin: 8px;
}

.flow-node:hover {
    border-color: #1f6feb;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.flow-node.active {
    border-color: #1f6feb;
    background: rgba(31, 111, 235, 0.1);
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2);
}

.flow-node.completed {
    border-color: #238636;
    background: rgba(35, 134, 54, 0.1);
}

.flow-node.running {
    border-color: #fd7e14;
    background: rgba(253, 126, 20, 0.1);
    animation: pulse 2s infinite;
}

.flow-node.pending {
    border-color: #30363d;
    background: #161b22;
}

.flow-node.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.node-icon {
    font-size: 24px;
    margin-bottom: 8px;
    color: #f0f6fc;
}

.node-title {
    font-size: 14px;
    font-weight: 500;
    color: #f0f6fc;
    text-align: center;
    margin-bottom: 4px;
}

.node-status {
    font-size: 12px;
    color: #8b949e;
    text-transform: uppercase;
    font-weight: 500;
}

.flow-connector {
    position: absolute;
    background-color: #30363d;
    z-index: 1;
}

.flow-connector.vertical {
    width: 2px;
    height: 20px;
    bottom: -22px;
    left: 50%;
    transform: translateX(-50%);
}

.flow-section:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 16px;
    background-color: #30363d;
    z-index: 1;
}

.parallel-group::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 12px;
    background-color: #30363d;
}

.parallel-group::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 12px;
    background-color: #30363d;
}

.parallel-group .flow-node:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -22px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 2px;
    background-color: #30363d;
}

.time-switcher {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-left: 16px;
}

.time-switcher label {
    font-size: 12px;
    color: #7d8590;
    font-weight: 600;
}

.time-switcher select {
    padding: 4px 8px;
    border: 1px solid #30363d;
    border-radius: 4px;
    background-color: #21262d;
    color: #f0f6fc;
    font-size: 12px;
    cursor: pointer;
}

.time-switcher select:focus {
    outline: none;
    border-color: #58a6ff;
    box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2);
}

.time-switcher select:hover {
    border-color: #58a6ff;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(253, 126, 20, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(253, 126, 20, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(253, 126, 20, 0);
    }
}

/* 右侧面板 - 步骤框列表形式 */
.right-panel {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.right-panel.collapsed {
    width: 40px;
    min-width: 40px;
}

.right-panel.collapsed .panel-content {
    display: none;
}

.panel-header {
    padding: 16px;
    border-bottom: 1px solid #30363d;
    background-color: #21262d;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.panel-title h3 {
    color: #f0f6fc;
    font-size: 16px;
    margin-bottom: 4px;
    font-weight: 600;
}

.panel-toggle {
    background: none;
    border: none;
    color: #7d8590;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.panel-toggle:hover {
    background-color: #30363d;
    color: #e6edf3;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: #0d1117;
}

/* Case信息区域 */
.case-info-section {
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #30363d;
    border-radius: 8px;
    background-color: #161b22;
}

.case-info-section h4 {
    color: #f0f6fc;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
}

.case-meta {
    color: #8b949e;
    font-size: 14px;
    margin-bottom: 12px;
}

.case-labels {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.label {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 4px;
    margin-bottom: 4px;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.label-bug {
    background: #f85149;
    color: #ffffff;
    border-color: #f85149;
}

.label-feature {
    background: #1f6feb;
    color: #ffffff;
    border-color: #1f6feb;
}

.label-enhancement {
    background: #7c3aed;
    color: #ffffff;
    border-color: #7c3aed;
}

.label-documentation {
    background: #0969da;
    color: #ffffff;
    border-color: #0969da;
}

.label-question {
    background: #d1242f;
    color: #ffffff;
    border-color: #d1242f;
}

.label-wontfix {
    background: #656d76;
    color: #ffffff;
    border-color: #656d76;
}

.label-duplicate {
    background: #6e7681;
    color: #ffffff;
    border-color: #6e7681;
}

.label-invalid {
    background: #8b5cf6;
    color: #ffffff;
    border-color: #8b5cf6;
}

.label-good-first-issue {
    background: #7c3aed;
    color: #ffffff;
    border-color: #7c3aed;
}

.label-help-wanted {
    background: #008672;
    color: #ffffff;
    border-color: #008672;
}

/* 项目和里程碑项目样式 */
.project-item,
.milestone-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    color: var(--text-primary);
}

.project-item::before {
    content: "\f542";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 8px;
    color: var(--text-secondary);
}

.milestone-item::before {
    content: "\f11e";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 8px;
    color: var(--text-secondary);
}

/* 步骤框容器 */
.steps-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 步骤框 */
.step-panel {
    background-color: #161b22;
    border: 2px solid #30363d;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.step-panel.active {
    border-color: #1f6feb;
    opacity: 1;
    box-shadow: 0 0 0 2px rgba(31, 111, 235, 0.2);
}

.step-panel.completed {
    border-color: #238636;
}

.step-panel.running {
    border-color: #fd7e14;
    animation: pulse 2s infinite;
}

.step-panel.disabled {
    opacity: 0.3;
    pointer-events: none;
}

/* 步骤框头部 */
.step-header {
    padding: 12px 16px;
    background-color: #21262d;
    border-bottom: 1px solid #30363d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.step-icon {
    font-size: 16px;
    color: #7d8590;
}

.step-panel.active .step-icon {
    color: #1f6feb;
}

.step-panel.completed .step-icon {
    color: #238636;
}

.step-panel.running .step-icon {
    color: #fd7e14;
}

.step-name {
    color: #f0f6fc;
    font-size: 14px;
    font-weight: 600;
}

.step-status {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.step-status.pending {
    background-color: #1f6feb;
    color: white;
}

.step-status.running {
    background-color: #fd7e14;
    color: white;
}

.step-status.completed {
    background-color: #238636;
    color: white;
}

.step-status.disabled {
    background-color: #484f58;
    color: #7d8590;
}

/* 步骤框内容 */
.step-content {
    padding: 16px;
    background-color: #0d1117;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #7d8590;
    text-align: center;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #30363d;
}

.empty-state p {
    font-size: 16px;
    margin: 0;
}

/* 输入面板样式 */
.input-panel {
    background-color: #21262d;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.input-group {
    margin-bottom: 16px;
}

.input-group label {
    display: block;
    color: #e6edf3;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
}

.input-group input,
.input-group select,
.input-group textarea {
    width: 100%;
    padding: 8px 12px;
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #e6edf3;
    font-size: 14px;
}

.input-group textarea {
    resize: vertical;
    min-height: 80px;
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.data-table {
    background-color: #21262d;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 16px;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #30363d;
}

.data-table th {
    background-color: #21262d;
    color: #f0f6fc;
    font-weight: 600;
    font-size: 12px;
}

.data-table td {
    color: #f0f6fc;
    font-size: 12px;
}

.data-table tr:hover {
    background-color: #30363d;
}

/* Map Gallery样式 */
.map-gallery-panel {
    padding: 16px;
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.gallery-controls select {
    padding: 4px 8px;
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 4px;
    color: #e6edf3;
    font-size: 12px;
}

.map-image-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.wafer-section,
.image-section {
    background-color: #21262d;
    border-radius: 8px;
    padding: 16px;
}

.wafer-section h5,
.image-section h5 {
    color: #e6edf3;
    margin-bottom: 12px;
    font-size: 14px;
}

.map-container {
    margin: 0 auto;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
}

.image-item {
    aspect-ratio: 4/3;
    background: linear-gradient(45deg, #30363d, #21262d);
    border: 1px solid #30363d;
    border-radius: 4px;
    position: relative;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .cases-layout {
        grid-template-columns: 280px 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .cases-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr 1fr;
    }
    
    .cases-layout.right-collapsed {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.page-content {
    animation: fadeIn 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #161b22;
}

::-webkit-scrollbar-thumb {
    background: #30363d;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #484f58;
}

/* Dashboard参数化查询样式 */
.query-section {
    background: #21262d;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid #30363d;
}

.query-section h3 {
    color: #f0f6fc;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
}

.query-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.query-params {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.param-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.param-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.param-group label {
    color: #f0f6fc;
    font-size: 14px;
    font-weight: 500;
}

.param-group .form-control {
    padding: 8px 12px;
    border: 1px solid #30363d;
    border-radius: 6px;
    background: #0d1117;
    color: #f0f6fc;
    font-size: 14px;
    transition: border-color 0.2s;
}

.param-group .form-control:focus {
    outline: none;
    border-color: #1f6feb;
    box-shadow: 0 0 0 3px rgba(31, 111, 235, 0.1);
}

.param-group .form-control::placeholder {
    color: #8b949e;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-range span {
    color: #8b949e;
    font-size: 14px;
}

.query-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 查询结果区域 */
.results-section {
    display: none; /* 默认隐藏，通过JavaScript控制显示 */
    margin-top: 30px;
    background-color: #161b22;
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 20px;
}

.results-section.show {
    display: block;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.results-header h3 {
    color: #f0f6fc;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.results-header h3 i {
    margin-right: 8px;
    color: #1f6feb;
}

.results-info {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #7d8590;
    font-size: 14px;
}

.results-table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    background: #0d1117;
    border-radius: 4px;
    overflow: hidden;
}

.results-table th,
.results-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #30363d;
}

.results-table th {
    background: #21262d;
    color: #f0f6fc;
    font-weight: 600;
    font-size: 14px;
}

.results-table td {
    color: #e6edf3;
    font-size: 14px;
}

.results-table tr:hover {
    background: #161b22;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
}

.page-info {
    color: #7d8590;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chart-row {
        grid-template-columns: 1fr;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .results-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 10px;
    }
}

/* 工作流section标题样式 */
.section-title {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #f0f6fc;
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #21262d;
    border-radius: 6px;
    border: 1px solid #30363d;
}

/* 新的工作流节点样式 */
.workflow-svg-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 800px;
    background: #0d1117;
    border-radius: 8px;
    overflow: hidden;
}

.workflow-nodes-container {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.workflow-node {
    position: absolute;
    width: 120px;
    height: 100px;
    border: 2px solid #30363d;
    border-radius: 8px;
    background: #21262d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.workflow-node:hover {
    transform: scale(1.05);
    z-index: 10;
}

.workflow-node.selected {
    border-color: #1f6feb !important;
    box-shadow: 0 0 20px rgba(31, 111, 235, 0.4) !important;
    z-index: 10;
}

.workflow-node .node-icon {
    font-size: 24px;
    margin-bottom: 4px;
    font-weight: bold;
}

.workflow-node .node-title {
    font-size: 12px;
    color: #f0f6fc;
    font-weight: 600;
    margin-bottom: 4px;
    line-height: 1.2;
}

.workflow-node .node-status {
    font-size: 10px;
    font-weight: 500;
}

/* 节点详情面板样式 */
.node-panel {
    background: #21262d;
    border-radius: 8px;
    border: 1px solid #30363d;
    margin-bottom: 16px;
    overflow: hidden;
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #161b22;
    border-bottom: 1px solid #30363d;
}

.node-title-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.node-icon-large {
    font-size: 32px;
    font-weight: bold;
    color: #1f6feb;
}

.node-info .node-name {
    font-size: 18px;
    font-weight: 600;
    color: #f0f6fc;
    margin-bottom: 4px;
}

.node-info .node-type {
    font-size: 12px;
    color: #7d8590;
    text-transform: uppercase;
    font-weight: 500;
}

.node-status {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.node-status.completed {
    background: rgba(35, 134, 54, 0.15);
    color: #238636;
    border: 1px solid #238636;
}

.node-status.running {
    background: rgba(248, 81, 73, 0.15);
    color: #f85149;
    border: 1px solid #f85149;
}

.node-status.pending {
    background: rgba(255, 140, 0, 0.15);
    color: #ff8c00;
    border: 1px solid #ff8c00;
}

.node-status.failed {
    background: rgba(218, 54, 51, 0.15);
    color: #da3633;
    border: 1px solid #da3633;
}

.node-status.disabled {
    background: rgba(110, 118, 129, 0.15);
    color: #6e7681;
    border: 1px solid #6e7681;
}

.node-content {
    padding: 16px;
}

.node-description h4 {
    color: #f0f6fc;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 600;
}

.node-description p {
    color: #7d8590;
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 16px;
}

.node-config-section {
    margin-bottom: 16px;
}

.node-config-section h4 {
    color: #f0f6fc;
    font-size: 14px;
    margin-bottom: 12px;
    font-weight: 600;
}

.config-grid {
    display: grid;
    gap: 12px;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.config-item label {
    font-size: 12px;
    color: #7d8590;
    font-weight: 500;
}

.config-item span {
    font-size: 13px;
    color: #f0f6fc;
    background: #161b22;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #30363d;
}

.node-io-section {
    margin-bottom: 16px;
}

.io-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #30363d;
}

.io-item:last-child {
    border-bottom: none;
}

.io-item label {
    font-size: 12px;
    color: #7d8590;
    font-weight: 500;
}

.io-item span {
    font-size: 13px;
    color: #f0f6fc;
}

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-buttons .btn {
    flex: 1;
    min-width: 100px;
}

/* SVG连接线样式 */
.workflow-svg-container svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.workflow-svg-container svg line {
    transition: all 0.3s ease;
}

.workflow-svg-container svg line:hover {
    stroke-width: 3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .workflow-node {
        width: 100px;
        height: 80px;
        padding: 6px;
    }
    
    .workflow-node .node-icon {
        font-size: 20px;
    }
    
    .workflow-node .node-title {
        font-size: 10px;
    }
    
    .workflow-node .node-status {
        font-size: 8px;
    }
    
    .node-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
}

/* 查询状态指示器 */
.query-status-indicator {
    background: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.query-status-indicator.filtered {
    background: rgba(31, 111, 235, 0.1);
    border-color: #1f6feb;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.status-content i {
    color: #7d8590;
    font-size: 16px;
}

.query-status-indicator.filtered .status-content i {
    color: #1f6feb;
}

.status-content span {
    color: #f0f6fc;
    font-size: 14px;
    font-weight: 500;
}

#resetQueryBtn {
    margin-left: auto;
}

/* 侧边栏隐藏按键 */
.sidebar-toggle {
    background: none;
    border: none;
    color: #7d8590;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-left: auto;
}

.sidebar-toggle:hover {
    background: #21262d;
    color: #e6edf3;
}

.sidebar-toggle i {
    font-size: 16px;
}

/* 侧边栏折叠状态 */
.sidebar.collapsed {
    width: 60px;
}

.sidebar.collapsed .sidebar-header h3 span,
.sidebar.collapsed .nav-item span {
    display: none;
}

.sidebar.collapsed .sidebar-toggle {
    margin-left: 0;
}

.sidebar.collapsed .user-info #currentUser {
    display: none;
}

/* GitHub风格的Case信息面板 */
.case-info-panel {
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    margin-bottom: 20px;
}

.info-section {
    border-bottom: 1px solid var(--border-color);
}

.info-section:last-child {
    border-bottom: none;
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.info-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.config-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.config-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.config-btn i {
    font-size: 14px;
}

.customize-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.customize-btn:hover {
    background: var(--primary-color);
    color: white;
}

.info-content {
    padding: 16px;
}

.empty-state {
    color: var(--text-secondary);
    font-size: 14px;
    font-style: italic;
}

/* Assignees样式 */
.assignee-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
}

.assignee-item span {
    font-size: 14px;
    color: var(--text-primary);
}

/* Development样式 */
.dev-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dev-item:hover {
    background: var(--border-color);
}

.dev-item i:first-child {
    color: var(--text-secondary);
    font-size: 14px;
}

.dev-item span {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
}

.dev-item i:last-child {
    color: var(--text-secondary);
    font-size: 12px;
}

.dev-link {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.dev-link a {
    color: var(--primary-color);
    text-decoration: none;
}

.dev-link a:hover {
    text-decoration: underline;
}

/* Notifications样式 */
.notification-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    margin-bottom: 12px;
}

.notification-btn:hover {
    background: var(--border-color);
}

.notification-btn i {
    font-size: 14px;
}

.notification-btn span {
    font-size: 14px;
}

.notification-text {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Participants样式 */
.participants-list {
    display: flex;
    gap: 4px;
}

.participants-list .avatar {
    width: 24px;
    height: 24px;
}

/* Actions样式 */
.action-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.action-item:last-child {
    border-bottom: none;
}

.action-item:hover {
    background: var(--bg-secondary);
    margin: 0 -16px;
    padding: 8px 16px;
}

.action-item i {
    font-size: 14px;
    color: var(--text-secondary);
    width: 16px;
}

.action-item span {
    font-size: 14px;
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar-header {
        padding: 15px;
    }
    
    .sidebar-toggle {
        padding: 6px;
    }
    
    .info-header {
        padding: 12px;
    }
    
    .info-content {
        padding: 12px;
    }
}

/* 面板标签样式 */
.panel-tabs {
    margin-top: 10px;
    border-bottom: 1px solid #30363d;
}

.tab-buttons {
    display: flex;
    gap: 0;
}

.tab-button {
    background: none;
    border: none;
    padding: 10px 16px;
    color: #7d8590;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.tab-button:hover {
    color: #f0f6fc;
    background-color: #21262d;
}

.tab-button.active {
    color: #f0f6fc;
    border-bottom-color: #1f6feb;
}

.tab-button i {
    font-size: 12px;
}

/* 调整面板头部样式以适应标签 */
.panel-header {
    flex-direction: column;
    align-items: stretch;
}

.panel-header .panel-title {
    margin-bottom: 0;
}

/* 节点状态样式 */
.workflow-node {
    transition: all 0.3s ease;
    border: 2px solid #ddd;
    cursor: pointer;
}

.workflow-node:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 不同状态的颜色 */
.workflow-node.status-pending {
    border-color: #6c757d;
    background-color: #f8f9fa;
}

.workflow-node.status-in-progress {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.workflow-node.status-completed {
    border-color: #28a745;
    background-color: #d4edda;
}

.workflow-node.status-failed {
    border-color: #dc3545;
    background-color: #f8d7da;
}

/* 状态指示器 */
.status-indicator {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    margin-top: 5px;
}

.status-indicator.status-pending {
    background-color: #6c757d;
    color: white;
}

.status-indicator.status-in-progress {
    background-color: #ffc107;
    color: #212529;
}

.status-indicator.status-completed {
    background-color: #28a745;
    color: white;
}

.status-indicator.status-failed {
    background-color: #dc3545;
    color: white;
}

/* 状态消息提示 */
.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.status-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 节点操作按钮 */
.btn-node-action, .btn-node-config {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    margin: 2px;
    transition: all 0.2s ease;
}

.btn-node-action {
    background-color: #28a745;
    color: white;
}

.btn-node-action:hover {
    background-color: #218838;
}

.btn-node-config {
    background-color: #6c757d;
    color: white;
}

.btn-node-config:hover {
    background-color: #5a6268;
}