<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DN Issue Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/style.css">
    <link rel="stylesheet" href="/static/case_info_panel.css">
</head>
<body>
    <div class="container">
        <!-- 左侧导航栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-bug"></i> DN System</h3>
                <button class="sidebar-toggle" onclick="toggleSidebar()" title="隐藏/显示菜单">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </li>
                    <li class="nav-item" data-page="cases">
                        <i class="fas fa-list-ul"></i>
                        <span>Cases</span>
                    </li>
                    <li class="nav-item" data-page="workflow">
                        <i class="fas fa-project-diagram"></i>
                        <span>Workflow</span>
                    </li>
                    <li class="nav-item" data-page="analytics">
                        <i class="fas fa-chart-line"></i>
                        <span>Analytics</span>
                    </li>
                    <li class="nav-item" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info" id="userInfo">
                    <i class="fas fa-user-circle"></i>
                    <span id="currentUser">Loading...</span>
                    <button class="logout-btn" onclick="logout()" title="登出">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- Dashboard 页面 -->
            <div class="page-content active" id="dashboard-page">
                <div class="page-header">
                    <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 查询状态指示器 -->
                <div class="query-status-indicator" id="queryStatusIndicator">
                    <div class="status-content">
                        <i class="fas fa-database"></i>
                        <span id="queryStatusText">显示全部数据</span>
                        <button class="btn btn-sm btn-secondary" id="resetQueryBtn" onclick="clearQuery()" style="display: none;">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCases">0</h3>
                            <p>Total Cases</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingCases">0</h3>
                            <p>Pending Cases</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="inProgressCases">0</h3>
                            <p>In Progress</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completedCases">0</h3>
                            <p>Completed</p>
                        </div>
                    </div>
                </div>

                <!-- 趋势图表区域 -->
                <div class="charts-section">
                    <div class="chart-row">
                        <div class="chart-container">
                            <h3><i class="fas fa-chart-line"></i> 案例趋势</h3>
                            <div class="chart-placeholder">
                                <canvas id="casesTrendChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container">
                            <h3><i class="fas fa-chart-bar"></i> 工具分布</h3>
                            <div class="chart-placeholder">
                                <canvas id="toolDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="chart-row">
                        <div class="chart-container">
                            <h3><i class="fas fa-chart-pie"></i> 状态分布</h3>
                            <div class="chart-placeholder">
                                <canvas id="statusDistributionChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container">
                            <h3><i class="fas fa-chart-area"></i> 缺陷类别</h3>
                            <div class="chart-placeholder">
                                <canvas id="defectClassChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速查询区域 -->
                <div class="filter-section">
                    <h3><i class="fas fa-search"></i> 快速查询</h3>
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label>Product ID</label>
                            <input type="text" id="productId" class="form-control" placeholder="请输入Product ID">
                        </div>
                        <div class="filter-group">
                            <label>Lot ID</label>
                            <input type="text" id="lotId" class="form-control" placeholder="请输入Lot ID">
                        </div>
                        <div class="filter-group">
                            <label>Layer ID</label>
                            <input type="text" id="layerId" class="form-control" placeholder="请输入Layer ID">
                        </div>
                        <div class="filter-group">
                            <label>Tool</label>
                            <select id="toolSelect" class="form-control">
                                <option value="">请选择Tool</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>时间范围</label>
                            <div class="date-range">
                                <input type="date" id="startDate" class="form-control">
                                <span>至</span>
                                <input type="date" id="endDate" class="form-control">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label>Defect Class</label>
                            <select id="defectClass" class="form-control">
                                <option value="">请选择Defect Class</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Priority</label>
                            <select id="prioritySelect" class="form-control">
                                <option value="">请选择Priority</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Status</label>
                            <select id="statusSelect" class="form-control">
                                <option value="">请选择Status</option>
                                <option value="open">Open</option>
                                <option value="in-progress">In Progress</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-primary" onclick="executeQuery()">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button class="btn btn-secondary" onclick="clearQuery()">
                            <i class="fas fa-eraser"></i> 清空
                        </button>
                    </div>
                </div>

                <!-- 查询结果区域 -->
                <div class="results-section" id="resultsSection">
                    <div class="results-header">
                        <h3><i class="fas fa-table"></i> 查询结果</h3>
                        <div class="results-info">
                            <span id="resultsCount">0</span> 条记录
                            <button class="btn btn-sm btn-secondary" onclick="exportResults()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                    <div class="results-table-container">
                        <table class="results-table" id="resultsTable">
                            <thead>
                                <tr>
                                    <th>Case ID</th>
                                    <th>Product ID</th>
                                    <th>Lot ID</th>
                                    <th>Layer ID</th>
                                    <th>Tool</th>
                                    <th>Defect Class</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Assignee</th>
                                    <th>Created Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                                <!-- 查询结果将在这里显示 -->
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination" id="pagination">
                        <button class="btn btn-sm btn-secondary" onclick="previousPage()">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="page-info">
                            第 <span id="currentPageNum">1</span> 页，共 <span id="totalPagesNum">1</span> 页
                        </span>
                        <button class="btn btn-sm btn-secondary" onclick="nextPage()">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Cases 页面 -->
            <div class="page-content" id="cases-page">
                <div class="page-header">
                    <h1><i class="fas fa-list-ul"></i> Cases</h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="createNewIssue()">
                            <i class="fas fa-plus"></i> 新建Case
                        </button>
                        <button class="btn btn-secondary" onclick="refreshIssues()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="cases-layout" id="casesLayout">
                    <!-- 左侧Case列表 -->
                    <div class="cases-list">
                        <div class="cases-header">
                            <h3>Case列表</h3>
                            <div class="search-box">
                                <input type="text" placeholder="搜索Case...">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        <div class="cases-container" id="casesContainer">
                            <!-- Case列表将在这里显示 -->
                        </div>
                    </div>

                    <!-- 中间工作流区域 -->
                    <div class="workflow-section">
                        <div class="workflow-header">
                            <h3><i class="fas fa-project-diagram"></i> 工作流程</h3>
                            <div style="display: flex; align-items: center; gap: 16px;">
                                <div class="time-switcher">
                                    <label>时间:</label>
                                    <select id="timeRange" onchange="handleTimeChange()">
                                        <option value="current">当前时间</option>
                                        <option value="1h">1小时前</option>
                                        <option value="6h">6小时前</option>
                                        <option value="1d">1天前</option>
                                        <option value="1w">1周前</option>
                                        <option value="1m">1个月前</option>
                                    </select>
                                </div>
                                <select id="workflowSelect" onchange="handleWorkflowChange()">
                                    <option value="dn-rd">DN RD工作流</option>
                                    <option value="dn-qa">DN QA工作流</option>
                                    <option value="dn-production" selected>DN生产工作流</option>
                                </select>
                            </div>
                        </div>
                        <div class="workflow-diagram" id="workflowDiagram">
                            <!-- Workflow diagram will be rendered here -->
                        </div>
                    </div>

                    <!-- 右侧详情面板 -->
                    <div class="right-panel" id="rightPanel">
                        <div class="panel-header">
                            <div class="panel-title">
                                <h3 id="panelTitle">工作流步骤</h3>
                                <div id="panelStatus"></div>
                            </div>
                            <button class="panel-toggle" onclick="toggleRightPanel()">
                                <i class="fas fa-chevron-right" id="toggleIcon"></i>
                            </button>
                        </div>
                        <div class="panel-content">
                            <!-- Case信息区域 -->
                            <div class="case-info-section" id="caseInfoSection">
                                <h4 id="caseTitle">请选择一个Case</h4>
                                <div class="case-meta">
                                    <span class="assignee">负责人: <span id="assigneeName">未分配</span></span>
                                    <span class="status">状态: <span id="caseStatus">-</span></span>
                                </div>
                                <div class="case-labels" id="caseLabels">
                                    <!-- 标签将在这里显示 -->
                                </div>
                            </div>
                            
                            <!-- 步骤框容器 -->
                            <div class="steps-container" id="stepsContainer">
                                <div class="empty-state">
                                    <i class="fas fa-project-diagram"></i>
                                    <p>选择左侧工作流节点查看详情</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Workflow 页面 -->
            <div class="page-content" id="workflow-page">
                <div class="page-header">
                    <h1><i class="fas fa-project-diagram"></i> Workflow Management</h1>
                </div>
                <div class="workflow-content">
                    <!-- 工作流选择器 -->
                    <div class="workflow-header" style="margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                            <div class="time-switcher">
                                <label>时间:</label>
                                <select id="workflowTimeRange" onchange="handleWorkflowTimeChange()">
                                    <option value="realtime" selected>实时</option>
                                    <option value="1h">1小时前</option>
                                    <option value="1d">1天前</option>
                                    <option value="1w">1周前</option>
                                    <option value="1m">1个月前</option>
                                </select>
                            </div>
                            <select id="workflowPageSelect" onchange="handleWorkflowPageChange()">
                                <option value="dn-rd">DN RD工作流</option>
                                <option value="dn-qa">DN QA工作流</option>
                                <option value="dn-production" selected>DN生产工作流</option>
                            </select>
                        </div>
                    </div>

                    <!-- 工作流图表 -->
                    <div class="workflow-diagram" id="workflowPageDiagram" style="min-height: 600px; background: #0d1117; border: 1px solid #30363d; border-radius: 8px;">
                        <!-- Workflow diagram will be rendered here -->
                    </div>
                </div>
            </div>

            <!-- Analytics 页面 -->
            <div class="page-content" id="analytics-page">
                <div class="page-header">
                    <h1><i class="fas fa-chart-line"></i> Analytics</h1>
                </div>
                <div class="analytics-content">
                    <p>分析报告功能开发中...</p>
                </div>
            </div>

            <!-- Settings 页面 -->
            <div class="page-content" id="settings-page">
                <div class="page-header">
                    <h1><i class="fas fa-cog"></i> Settings</h1>
                </div>
                <div class="settings-content">
                    <p>系统设置功能开发中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 包含Case信息侧边栏组件 -->
    {% include 'case_info_panel.html' %}

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/script.js?v=5"></script>
    <script src="/static/case_info_panel.js"></script>
</body>
</html> 