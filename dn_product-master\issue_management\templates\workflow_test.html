<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0d1117;
            color: #e6edf3;
            padding: 2rem;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .section h2 {
            color: #58a6ff;
            margin-bottom: 1rem;
        }
        .loading {
            color: #f85149;
        }
        .success {
            color: #3fb950;
        }
        .error {
            color: #f85149;
        }
        pre {
            background: #0d1117;
            padding: 1rem;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.875rem;
        }
        button {
            background: #238636;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 0.5rem;
        }
        button:hover {
            background: #2ea043;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 工作流API连接测试</h1>
        
        <div class="section">
            <h2>API连接测试</h2>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="testProductionWorkflow()">测试Production工作流</button>
            <div id="apiStatus" class="loading">等待测试...</div>
        </div>
        
        <div class="section">
            <h2>工作流数据</h2>
            <div id="workflowData">
                <pre id="workflowJson">暂无数据</pre>
            </div>
        </div>
        
        <div class="section">
            <h2>节点信息</h2>
            <div id="nodeInfo">
                <pre id="nodeJson">暂无数据</pre>
            </div>
        </div>
    </div>

    <script>
        async function testAPI() {
            const status = document.getElementById('apiStatus');
            status.textContent = '正在测试API连接...';
            status.className = 'loading';
            
            try {
                const response = await fetch('/api/workflows');
                const data = await response.json();
                
                status.textContent = `✅ API连接成功! 找到 ${data.workflows.length} 个工作流`;
                status.className = 'success';
                
                document.getElementById('workflowJson').textContent = JSON.stringify(data, null, 2);
                
            } catch (error) {
                status.textContent = `❌ API连接失败: ${error.message}`;
                status.className = 'error';
            }
        }
        
        async function testProductionWorkflow() {
            const status = document.getElementById('apiStatus');
            status.textContent = '正在测试Production工作流...';
            status.className = 'loading';
            
            try {
                const response = await fetch('/api/workflows');
                const data = await response.json();
                
                const productionWorkflow = data.workflows.find(w => w.id === 'dn-production');
                
                if (productionWorkflow && productionWorkflow.config && productionWorkflow.config.nodes) {
                    status.textContent = `✅ 找到Production工作流! 包含 ${productionWorkflow.config.nodes.length} 个节点`;
                    status.className = 'success';
                    
                    document.getElementById('nodeJson').textContent = JSON.stringify(productionWorkflow.config.nodes, null, 2);
                    
                    // 分析节点类型
                    const nodeTypes = {};
                    productionWorkflow.config.nodes.forEach(node => {
                        nodeTypes[node.type] = (nodeTypes[node.type] || 0) + 1;
                    });
                    
                    status.textContent += `\n节点类型分布: ${JSON.stringify(nodeTypes)}`;
                    
                } else {
                    status.textContent = '❌ 未找到Production工作流或节点数据';
                    status.className = 'error';
                }
                
            } catch (error) {
                status.textContent = `❌ 测试失败: ${error.message}`;
                status.className = 'error';
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            testAPI();
        });
    </script>
</body>
</html> 