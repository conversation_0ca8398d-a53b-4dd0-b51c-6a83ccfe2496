# DN产品工作流节点开发规范

## 1. 通用节点架构设计

### 1.1 四种通用节点类型
系统包含四种核心节点类型，通过配置参数来实现不同的功能：

```javascript
// 通用节点基类
class BaseWorkflowNode {
    constructor(nodeConfig) {
        this.id = nodeConfig.id;
        this.type = nodeConfig.type; // 'query' | 'algorithm' | 'processing' | 'display'
        this.name = nodeConfig.name;
        this.config = nodeConfig.config;
        this.permissions = nodeConfig.permissions || [];
        this.inputSchema = nodeConfig.inputSchema || {};
        this.outputSchema = nodeConfig.outputSchema || {};
        this.status = 'pending';
    }
    
    async execute(inputData) {
        try {
            this.validate(inputData);
            const result = await this.process(inputData);
            return this.formatOutput(result);
        } catch (error) {
            return this.handleError(error);
        }
    }
    
    validate(inputData) {
        return this.validateBySchema(inputData, this.inputSchema);
    }
    
    async process(inputData) {
        throw new Error('Process method must be implemented by subclass');
    }
}
```

### 1.2 QueryNode (查询节点)
**所有数据查询功能都使用QueryNode，通过不同SQL模板和数据处理器实现**

```javascript
class QueryNode extends BaseWorkflowNode {
    constructor(nodeConfig) {
        super({
            type: 'query',
            ...nodeConfig
        });
        
        this.sqlTemplate = nodeConfig.sqlTemplate;
        this.dataProcessor = nodeConfig.dataProcessor;
        this.cacheConfig = nodeConfig.cacheConfig || {};
    }
    
    async process(inputData) {
        // 1. 构建查询SQL
        const sql = this.buildQuery(inputData);
        
        // 2. 执行查询
        const rawData = await this.executeQuery(sql, inputData);
        
        // 3. 数据处理
        const processedData = this.processData(rawData, inputData);
        
        // 4. 权限过滤
        const filteredData = this.filterByPermissions(processedData);
        
        return filteredData;
    }
    
    buildQuery(inputData) {
        let sql = this.sqlTemplate;
        Object.keys(inputData).forEach(key => {
            sql = sql.replace(new RegExp(`{{${key}}}`, 'g'), inputData[key]);
        });
        return sql;
    }
}
```

### 1.3 AlgorithmNode (算法节点)
**所有算法分析功能都使用AlgorithmNode，包括规则算法和机器学习算法**

```javascript
class AlgorithmNode extends BaseWorkflowNode {
    constructor(nodeConfig) {
        super({
            type: 'algorithm',
            ...nodeConfig
        });
        
        this.algorithmType = nodeConfig.algorithmType; // 'rule' | 'ml' | 'statistical'
        this.algorithmConfig = nodeConfig.algorithmConfig || {};
        this.ruleEngine = nodeConfig.ruleEngine;
        this.modelPath = nodeConfig.modelPath;
    }
    
    async process(inputData) {
        switch (this.algorithmType) {
            case 'rule':
                return await this.executeRuleAlgorithm(inputData);
            case 'ml':
                return await this.executeMlAlgorithm(inputData);
            case 'statistical':
                return await this.executeStatisticalAlgorithm(inputData);
            default:
                throw new Error(`Unknown algorithm type: ${this.algorithmType}`);
        }
    }
    
    async executeRuleAlgorithm(inputData) {
        // 执行规则算法，如Robot Analysis
        const rules = this.algorithmConfig.rules;
        const ruleResults = [];
        
        for (const rule of rules) {
            const result = await this.evaluateRule(rule, inputData);
            ruleResults.push(result);
        }
        
        return {
            algorithmType: 'rule',
            results: ruleResults,
            summary: this.summarizeRuleResults(ruleResults)
        };
    }
    
    async executeStatisticalAlgorithm(inputData) {
        // 执行统计算法，如Tool Commonality机台相关性分析
        const correlationMatrix = this.calculateCorrelation(inputData);
        const significanceTests = this.performSignificanceTests(correlationMatrix);
        
        return {
            algorithmType: 'statistical',
            correlationMatrix: correlationMatrix,
            significanceTests: significanceTests,
            commonalityScore: this.calculateCommonalityScore(correlationMatrix)
        };
    }
}
```

### 1.4 ProcessingNode (数据处理节点)
**所有数据转换和处理功能都使用ProcessingNode**

```javascript
class ProcessingNode extends BaseWorkflowNode {
    constructor(nodeConfig) {
        super({
            type: 'processing',
            ...nodeConfig
        });
        
        this.processingType = nodeConfig.processingType; // 'transform' | 'aggregate' | 'generate'
        this.processingConfig = nodeConfig.processingConfig || {};
        this.outputFormat = nodeConfig.outputFormat;
    }
    
    async process(inputData) {
        switch (this.processingType) {
            case 'transform':
                return await this.transformData(inputData);
            case 'aggregate':
                return await this.aggregateData(inputData);
            case 'generate':
                return await this.generateData(inputData);
            default:
                throw new Error(`Unknown processing type: ${this.processingType}`);
        }
    }
    
    async transformData(inputData) {
        // 数据转换逻辑
        const transformRules = this.processingConfig.transformRules;
        const transformedData = {};
        
        for (const [outputField, rule] of Object.entries(transformRules)) {
            transformedData[outputField] = this.applyTransformRule(rule, inputData);
        }
        
        return transformedData;
    }
    
    async generateData(inputData) {
        // 数据生成逻辑，如生成Defect Map、Trend图数据
        const generationType = this.processingConfig.generationType;
        
        switch (generationType) {
            case 'defect_map':
                return await this.generateDefectMap(inputData);
            case 'trend_data':
                return await this.generateTrendData(inputData);
            default:
                throw new Error(`Unknown generation type: ${generationType}`);
        }
    }
}
```

### 1.5 DisplayNode (展示节点)
**所有数据展示功能都使用DisplayNode，包括图表、地图、表格等**

```javascript
class DisplayNode extends BaseWorkflowNode {
    constructor(nodeConfig) {
        super({
            type: 'display',
            ...nodeConfig
        });
        
        this.displayMode = nodeConfig.displayMode; // 'chart' | 'map' | 'table' | 'gallery'
        this.renderOptions = nodeConfig.renderOptions || {};
        this.interactionConfig = nodeConfig.interactionConfig || {};
    }
    
    async process(inputData) {
        const processedData = this.preprocessData(inputData);
        const renderConfig = this.generateRenderConfig(processedData);
        const interactionConfig = this.generateInteractionConfig(processedData);
        
        return {
            data: processedData,
            displayMode: this.displayMode,
            renderConfig: renderConfig,
            interactionConfig: interactionConfig
        };
    }
    
    generateRenderConfig(data) {
        switch (this.displayMode) {
            case 'chart':
                return this.generateChartConfig(data);
            case 'map':
                return this.generateMapConfig(data);
            case 'table':
                return this.generateTableConfig(data);
            case 'gallery':
                return this.generateGalleryConfig(data);
            default:
                return this.renderOptions;
        }
    }
    
    generateChartConfig(data) {
        // 生成图表配置，如Defect Trend图
        return {
            chartType: this.renderOptions.chartType || 'line',
            xAxis: this.renderOptions.xAxis,
            yAxis: this.renderOptions.yAxis,
            series: this.prepareSeries(data),
            options: this.renderOptions.chartOptions || {}
        };
    }
    
    generateMapConfig(data) {
        // 生成地图配置，如Defect Map
        return {
            mapType: this.renderOptions.mapType || 'heatmap',
            coordinates: this.extractCoordinates(data),
            values: this.extractValues(data),
            colorScale: this.renderOptions.colorScale || 'viridis',
            options: this.renderOptions.mapOptions || {}
        };
    }
}
```

## 2. 具体节点配置示例

### 2.1 数据查询节点配置

```javascript
// Case Info 查询节点
const caseInfoQueryConfig = {
    id: 'case-info-query',
    name: 'Case Info Query',
    type: 'query',
    sqlTemplate: `
        SELECT * FROM case_info 
        WHERE time >= '{{time}}' 
        AND product_id = '{{product_id}}'
        AND lot_id = '{{lot_id}}'
    `,
    dataProcessor: (rawData, inputData) => ({
        basicInfo: rawData.basicInfo,
        linkedFields: rawData.linkedFields
    }),
    permissions: {
        'ye': ['read', 'write', 'execute'],
        'module': ['read']
    }
};

// Hold Lot List 查询节点
const holdLotListQueryConfig = {
    id: 'hold-lot-list-query',
    name: 'Hold Lot List Query',
    type: 'query',
    sqlTemplate: `
        SELECT lot_id, hold_reason, hold_time, release_time 
        FROM hold_lot_list 
        WHERE status = 'HOLD' 
        AND hold_time >= '{{start_time}}'
    `,
    dataProcessor: (rawData, inputData) => ({
        holdLots: rawData,
        totalCount: rawData.length,
        summary: {
            activeHolds: rawData.filter(lot => !lot.release_time).length
        }
    })
};

// Lot History 查询节点
const lotHistoryQueryConfig = {
    id: 'lot-history-query',
    name: 'Lot History Query',
    type: 'query',
    sqlTemplate: `
        SELECT * FROM lot_history 
        WHERE lot_id = '{{lot_id}}'
        ORDER BY process_time DESC
    `,
    dataProcessor: (rawData, inputData) => ({
        history: rawData,
        processSteps: rawData.map(h => h.process_step),
        timeline: rawData.map(h => ({
            time: h.process_time,
            step: h.process_step,
            tool: h.tool_id
        }))
    })
};
```

### 2.2 算法分析节点配置

```javascript
// Robot Analysis 规则算法节点
const robotAnalysisConfig = {
    id: 'robot-analysis',
    name: 'Robot Analysis',
    type: 'algorithm',
    algorithmType: 'rule',
    algorithmConfig: {
        rules: [
            {
                id: 'defect_pattern_rule',
                condition: 'defect_density > 0.05 AND pattern_regularity > 0.8',
                action: 'flag_as_systematic_defect',
                priority: 'high'
            },
            {
                id: 'tool_correlation_rule',
                condition: 'tool_commonality_score > 0.7',
                action: 'suggest_tool_maintenance',
                priority: 'medium'
            },
            {
                id: 'process_deviation_rule',
                condition: 'process_parameter_deviation > 3_sigma',
                action: 'flag_process_issue',
                priority: 'high'
            }
        ],
        ruleEngine: 'simple_rule_engine',
        outputFormat: 'structured_analysis'
    },
    inputSchema: {
        defectData: { type: 'array', required: true },
        processData: { type: 'object', required: true },
        toolData: { type: 'object', required: true }
    },
    outputSchema: {
        analysisResults: { type: 'array' },
        recommendations: { type: 'array' },
        confidence: { type: 'number' }
    }
};

// Tool Commonality 机台相关性分析节点
const toolCommonalityConfig = {
    id: 'tool-commonality',
    name: 'Tool Commonality Analysis',
    type: 'algorithm',
    algorithmType: 'statistical',
    algorithmConfig: {
        correlationMethod: 'pearson', // 'spearman', 'kendall'
        significanceLevel: 0.05,
        minSampleSize: 30,
        analysisFeatures: [
            'defect_count',
            'defect_density',
            'process_parameters',
            'maintenance_history'
        ],
        outputMetrics: [
            'correlation_coefficient',
            'p_value',
            'confidence_interval',
            'commonality_score'
        ]
    },
    inputSchema: {
        toolData: { type: 'array', required: true },
        defectData: { type: 'array', required: true },
        timeRange: { type: 'object', required: true }
    },
    outputSchema: {
        correlationMatrix: { type: 'object' },
        significanceTests: { type: 'object' },
        commonalityScore: { type: 'number' },
        recommendations: { type: 'array' }
    }
};
```

### 2.3 数据处理节点配置

```javascript
// Defect Map 生成处理节点
const defectMapProcessingConfig = {
    id: 'defect-map-processing',
    name: 'Defect Map Processing',
    type: 'processing',
    processingType: 'generate',
    processingConfig: {
        generationType: 'defect_map',
        mapResolution: '1024x1024',
        colorMapping: {
            noDefect: '#00ff00',
            lowDefect: '#ffff00',
            mediumDefect: '#ff8000',
            highDefect: '#ff0000'
        },
        aggregationMethod: 'density_based',
        smoothingFilter: 'gaussian'
    },
    inputSchema: {
        defectCoordinates: { type: 'array', required: true },
        waferSize: { type: 'object', required: true },
        defectTypes: { type: 'array', required: false }
    },
    outputSchema: {
        mapImageData: { type: 'string' }, // base64 encoded image
        mapMetadata: { type: 'object' },
        defectStatistics: { type: 'object' }
    }
};

// Defect Trend 数据处理节点
const defectTrendProcessingConfig = {
    id: 'defect-trend-processing',
    name: 'Defect Trend Processing',
    type: 'processing',
    processingType: 'generate',
    processingConfig: {
        generationType: 'trend_data',
        timeGranularity: 'daily', // 'hourly', 'weekly', 'monthly'
        trendMetrics: [
            'defect_count',
            'defect_density',
            'yield_rate',
            'process_capability'
        ],
        smoothingWindow: 7, // days
        anomalyDetection: true
    },
    inputSchema: {
        historicalData: { type: 'array', required: true },
        timeRange: { type: 'object', required: true },
        metrics: { type: 'array', required: false }
    },
    outputSchema: {
        trendData: { type: 'array' },
        trendAnalysis: { type: 'object' },
        anomalies: { type: 'array' }
    }
};
```

### 2.4 展示节点配置

```javascript
// Defect Map 展示节点
const defectMapDisplayConfig = {
    id: 'defect-map-display',
    name: 'Defect Map Display',
    type: 'display',
    displayMode: 'map',
    renderOptions: {
        mapType: 'heatmap',
        colorScale: 'viridis',
        showLegend: true,
        showTooltip: true,
        zoomEnabled: true,
        panEnabled: true,
        overlayOptions: {
            showGrid: true,
            showCoordinates: true,
            showDefectDetails: true
        }
    },
    interactionConfig: {
        clickToZoom: true,
        hoverTooltip: true,
        selectRegion: true,
        exportOptions: ['png', 'svg', 'pdf']
    }
};

// Defect Trend 图表展示节点
const defectTrendDisplayConfig = {
    id: 'defect-trend-display',
    name: 'Defect Trend Chart Display',
    type: 'display',
    displayMode: 'chart',
    renderOptions: {
        chartType: 'line',
        xAxis: {
            field: 'time',
            title: 'Time',
            format: 'datetime'
        },
        yAxis: {
            field: 'defect_count',
            title: 'Defect Count',
            format: 'number'
        },
        series: [
            {
                name: 'Defect Count',
                field: 'defect_count',
                color: '#1890ff',
                type: 'line'
            },
            {
                name: 'Trend Line',
                field: 'trend_line',
                color: '#ff4d4f',
                type: 'line',
                style: 'dashed'
            }
        ],
        showLegend: true,
        showTooltip: true,
        zoomEnabled: true
    },
    interactionConfig: {
        brushSelection: true,
        crossfilter: true,
        exportOptions: ['png', 'svg', 'csv']
    }
};
```

## 3. 工作流节点串联配置

### 3.1 典型缺陷分析工作流

```javascript
const defectAnalysisWorkflow = {
    id: 'defect-analysis-workflow',
    name: 'YE Case管理业务流程',
    description: '完整的缺陷分析工作流程',
    nodes: [
        // 数据查询阶段
        {
            instanceId: 'case-info-001',
            configId: 'case-info-query',
            position: { x: 100, y: 100 },
            title: 'YE Case管理业务流程'
        },
        {
            instanceId: 'hold-lot-001',
            configId: 'hold-lot-list-query',
            position: { x: 300, y: 50 },
            title: 'Hold Lot List 查询'
        },
        {
            instanceId: 'lot-history-001',
            configId: 'lot-history-query',
            position: { x: 150, y: 200 },
            title: 'Lot History 查询'
        },
        
        // 算法分析阶段
        {
            instanceId: 'robot-analysis-001',
            configId: 'robot-analysis',
            position: { x: 500, y: 150 },
            title: 'Robot Analysis 算法分析'
        },
        {
            instanceId: 'tool-commonality-001',
            configId: 'tool-commonality',
            position: { x: 700, y: 100 },
            title: 'Tool Commonality 相关性分析'
        },
        
        // 数据处理阶段
        {
            instanceId: 'defect-map-proc-001',
            configId: 'defect-map-processing',
            position: { x: 300, y: 350 },
            title: 'Defect Map绘制'
        },
        {
            instanceId: 'trend-proc-001',
            configId: 'defect-trend-processing',
            position: { x: 500, y: 300 },
            title: 'Defect Trend 图绘制'
        },
        
        // 展示阶段
        {
            instanceId: 'defect-map-display-001',
            configId: 'defect-map-display',
            position: { x: 300, y: 450 },
            title: 'Defect Map展示'
        },
        {
            instanceId: 'trend-display-001',
            configId: 'defect-trend-display',
            position: { x: 500, y: 450 },
            title: 'Defect Trend 图展示'
        }
    ],
    connections: [
        // 数据查询阶段连接
        {
            from: 'case-info-001',
            to: 'hold-lot-001',
            dataMapping: {
                'lot_id': 'data.basicInfo.lot_id',
                'start_time': 'data.basicInfo.time'
            }
        },
        {
            from: 'case-info-001',
            to: 'lot-history-001',
            dataMapping: {
                'lot_id': 'data.basicInfo.lot_id'
            }
        },
        
        // 算法分析阶段连接
        {
            from: 'lot-history-001',
            to: 'robot-analysis-001',
            dataMapping: {
                'defectData': 'data.history',
                'processData': 'data.processSteps',
                'toolData': 'data.timeline'
            }
        },
        {
            from: 'hold-lot-001',
            to: 'tool-commonality-001',
            dataMapping: {
                'toolData': 'data.holdLots',
                'timeRange': 'data.summary'
            }
        },
        
        // 数据处理阶段连接
        {
            from: 'robot-analysis-001',
            to: 'defect-map-proc-001',
            dataMapping: {
                'defectCoordinates': 'data.analysisResults',
                'waferSize': 'data.recommendations'
            }
        },
        {
            from: 'tool-commonality-001',
            to: 'trend-proc-001',
            dataMapping: {
                'historicalData': 'data.correlationMatrix',
                'timeRange': 'data.significanceTests'
            }
        },
        
        // 展示阶段连接
        {
            from: 'defect-map-proc-001',
            to: 'defect-map-display-001',
            dataMapping: {
                'mapData': 'data.mapImageData',
                'metadata': 'data.mapMetadata'
            }
        },
        {
            from: 'trend-proc-001',
            to: 'trend-display-001',
            dataMapping: {
                'chartData': 'data.trendData',
                'analysis': 'data.trendAnalysis'
            }
        }
    ]
};
```

## 4. 节点工厂和注册系统

```javascript
class NodeFactory {
    static createNode(nodeConfig) {
        switch (nodeConfig.type) {
            case 'query':
                return new QueryNode(nodeConfig);
            case 'algorithm':
                return new AlgorithmNode(nodeConfig);
            case 'processing':
                return new ProcessingNode(nodeConfig);
            case 'display':
                return new DisplayNode(nodeConfig);
            default:
                throw new Error(`Unknown node type: ${nodeConfig.type}`);
        }
    }
}

class NodeRegistry {
    constructor() {
        this.nodeConfigs = new Map();
        this.nodeInstances = new Map();
    }
    
    registerAllConfigs() {
        // 注册查询节点
        this.registerNodeConfig('case-info-query', caseInfoQueryConfig);
        this.registerNodeConfig('hold-lot-list-query', holdLotListQueryConfig);
        this.registerNodeConfig('lot-history-query', lotHistoryQueryConfig);
        
        // 注册算法节点
        this.registerNodeConfig('robot-analysis', robotAnalysisConfig);
        this.registerNodeConfig('tool-commonality', toolCommonalityConfig);
        
        // 注册处理节点
        this.registerNodeConfig('defect-map-processing', defectMapProcessingConfig);
        this.registerNodeConfig('defect-trend-processing', defectTrendProcessingConfig);
        
        // 注册展示节点
        this.registerNodeConfig('defect-map-display', defectMapDisplayConfig);
        this.registerNodeConfig('defect-trend-display', defectTrendDisplayConfig);
    }
    
    registerNodeConfig(id, config) {
        this.nodeConfigs.set(id, config);
    }
    
    createWorkflowInstance(workflowConfig) {
        const workflowInstance = {
            id: workflowConfig.id,
            name: workflowConfig.name,
            nodes: new Map(),
            connections: workflowConfig.connections
        };
        
        // 创建所有节点实例
        for (const nodeSpec of workflowConfig.nodes) {
            const nodeConfig = this.nodeConfigs.get(nodeSpec.configId);
            const nodeInstance = NodeFactory.createNode({
                ...nodeConfig,
                id: nodeSpec.instanceId,
                position: nodeSpec.position,
                title: nodeSpec.title
            });
            
            workflowInstance.nodes.set(nodeSpec.instanceId, nodeInstance);
        }
        
        return workflowInstance;
    }
}
```

这个更新后的架构包含了四种通用节点类型，能够支持完整的工作流编排，包括数据查询、算法分析（规则和统计）、数据处理转换、以及各种展示形式。

您觉得这样的抽象设计是否更符合您的想法？ 