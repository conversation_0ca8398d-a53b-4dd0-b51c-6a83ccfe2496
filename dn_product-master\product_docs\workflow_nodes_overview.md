# DN产品工作流节点概述

## 项目背景
DN (Defect Navigation) 产品是一个半导体缺陷分析工作流管理系统，通过流程编排节点来实现缺陷检测、分析和处理的自动化流程。

## 通用节点架构

### 核心设计理念
系统采用**通用节点架构**，只有四种基础节点类型，通过配置参数实现不同的功能：

1. **QueryNode (查询节点)** - 所有数据查询功能使用同一个类，通过不同的SQL模板实现不同的查询逻辑
2. **AlgorithmNode (算法节点)** - 所有算法分析功能使用同一个类，包括规则算法和统计算法
3. **ProcessingNode (处理节点)** - 所有数据转换和处理功能使用同一个类，包括数据生成和格式转换
4. **DisplayNode (展示节点)** - 所有数据展示功能使用同一个类，通过不同的渲染模式实现不同的展示效果

### 节点配置化实现
每个具体功能都通过配置文件定义，而不是创建新的节点类：

```javascript
// 查询节点配置示例
const queryNodeConfig = {
    type: 'query',
    sqlTemplate: 'SELECT * FROM table WHERE condition = {{param}}',
    dataProcessor: (data) => { /* 数据处理逻辑 */ },
    permissions: { /* 权限配置 */ }
};

// 算法节点配置示例
const algorithmNodeConfig = {
    type: 'algorithm',
    algorithmType: 'rule', // 'rule' | 'statistical' | 'ml'
    algorithmConfig: {
        rules: [/* 规则定义 */],
        correlationMethod: 'pearson'
    }
};

// 处理节点配置示例
const processingNodeConfig = {
    type: 'processing',
    processingType: 'generate', // 'transform' | 'aggregate' | 'generate'
    processingConfig: {
        generationType: 'defect_map',
        mapResolution: '1024x1024'
    }
};

// 展示节点配置示例
const displayNodeConfig = {
    type: 'display',
    displayMode: 'chart', // 'table' | 'gallery' | 'chart' | 'map'
    renderOptions: { /* 渲染选项 */ }
};
```

## 具体节点功能分析

### 1. 数据查询节点 (QueryNode)

#### 1.1 Case Info 查询
- **功能**: 查询基础Case信息
- **输入**: time, product_id, lot_id等
- **输出**: basicInfo, linkedFields
- **权限**: YE用户可读写，Module用户只读

#### 1.2 Hold Lot List 查询
- **功能**: 查询Hold状态的Lot列表
- **输入**: start_time, status等
- **输出**: holdLots, totalCount, summary
- **特点**: 实时状态查询

#### 1.3 Lot History 查询
- **功能**: 查询Lot的历史处理信息
- **输入**: lot_id
- **输出**: history, processSteps, timeline
- **特点**: 时间序列数据

### 2. 算法分析节点 (AlgorithmNode)

#### 2.1 Robot Analysis (规则算法)
- **算法类型**: 规则引擎 (Rule-based Algorithm)
- **功能**: 基于预定义规则进行缺陷模式分析
- **规则示例**:
  - 缺陷密度规则: `defect_density > 0.05 AND pattern_regularity > 0.8`
  - 机台关联规则: `tool_commonality_score > 0.7`
  - 工艺偏差规则: `process_parameter_deviation > 3_sigma`
- **输出**: 结构化分析结果、建议、置信度

#### 2.2 Tool Commonality (机台相关性分析)
- **算法类型**: 统计分析算法 (Statistical Algorithm)
- **功能**: 分析不同机台之间的缺陷相关性
- **分析内容**:
  - 机台间缺陷数量相关性
  - 机台间缺陷密度相关性
  - 工艺参数相关性
  - 维护历史关联性
- **统计方法**: Pearson/Spearman/Kendall相关系数
- **输出**: 相关性矩阵、显著性检验、共性评分

### 3. 数据处理节点 (ProcessingNode)

#### 3.1 Defect Map 生成
- **处理类型**: 数据生成 (Generate)
- **功能**: 将缺陷坐标数据转换为热力图
- **输入**: 缺陷坐标、晶圆尺寸、缺陷类型
- **输出**: 地图图像数据、元数据、统计信息
- **特点**: 支持多种颜色映射和平滑滤波

#### 3.2 Defect Trend 数据处理
- **处理类型**: 数据生成 (Generate)
- **功能**: 生成缺陷趋势分析数据
- **输入**: 历史数据、时间范围、指标配置
- **输出**: 趋势数据、趋势分析、异常检测
- **特点**: 支持多种时间粒度和平滑算法

### 4. 数据展示节点 (DisplayNode)

#### 4.1 Defect Map 展示
- **展示模式**: 地图模式 (Map)
- **功能**: 交互式缺陷热力图展示
- **特点**: 支持缩放、平移、区域选择、导出
- **交互**: 点击缩放、悬停提示、区域选择

#### 4.2 Defect Trend 图表展示
- **展示模式**: 图表模式 (Chart)
- **功能**: 时间序列趋势图表展示
- **特点**: 支持多系列、缩放、刷选、导出
- **交互**: 刷选、交叉过滤、导出多种格式

## 工作流串联架构

### 典型缺陷分析工作流
```
[Case Info Query] ──┬─→ [Hold Lot List Query] ──→ [Tool Commonality Analysis]
                    │                                      │
                    └─→ [Lot History Query] ──→ [Robot Analysis] ──┬─→ [Defect Map Processing] ──→ [Defect Map Display]
                                                                    │
                                                                    └─→ [Defect Trend Processing] ──→ [Defect Trend Display]
```

### 数据流转机制
1. **数据查询阶段**: 收集基础数据 (Case Info, Hold Lot, Lot History)
2. **算法分析阶段**: 执行规则分析和统计分析 (Robot Analysis, Tool Commonality)
3. **数据处理阶段**: 生成可视化数据 (Defect Map, Trend Data)
4. **展示阶段**: 交互式展示结果 (Map Display, Chart Display)

## 技术特性

### 1. 通用性
- 四种基础节点类型覆盖所有功能需求
- 配置驱动，无需修改代码即可扩展功能
- 统一的接口和数据流规范

### 2. 可扩展性
- 新增查询只需配置SQL模板
- 新增算法只需配置算法类型和参数
- 新增展示只需配置渲染模式和选项

### 3. 灵活性
- 支持多种算法类型：规则、统计、机器学习
- 支持多种展示模式：表格、图表、地图、画廊
- 支持复杂的工作流编排和数据映射

### 4. 权限控制
- 基于角色的权限管理 (YE用户 vs Module用户)
- 节点级别的权限控制
- 操作级别的权限验证

## 应用场景

### 1. 缺陷分析工作流
- 从数据查询到结果展示的完整流程
- 支持多种分析算法和展示方式
- 适用于不同类型的缺陷分析需求

### 2. 机台相关性分析
- 通过Tool Commonality算法分析机台间关联
- 支持多种统计方法和显著性检验
- 为设备维护和工艺优化提供数据支持

### 3. 规则引擎应用
- 通过Robot Analysis执行预定义规则
- 支持复杂的条件判断和动作执行
- 适用于自动化的缺陷检测和分类

### 4. 可视化展示
- 支持多种数据可视化方式
- 提供丰富的交互功能
- 满足不同用户的数据查看需求

## 开发优势

### 1. 开发效率
- 通用节点架构减少重复开发
- 配置化实现快速功能扩展
- 标准化接口降低集成复杂度

### 2. 维护性
- 统一的代码结构和规范
- 配置与代码分离便于维护
- 模块化设计支持独立升级

### 3. 测试性
- 标准化的节点接口便于单元测试
- 配置化的功能便于集成测试
- 工作流级别的端到端测试

### 4. 部署性
- 轻量级的节点实现
- 灵活的配置管理
- 支持动态配置更新

这个通用节点架构为DN产品提供了强大而灵活的工作流编排能力，既满足了当前的功能需求，又为未来的扩展提供了良好的基础。 