<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据查询器节点编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 24px 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .node-icon {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .content {
            padding: 32px;
        }

        .section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-icon {
            width: 20px;
            height: 20px;
            background: #4f46e5;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .required {
            color: #ef4444;
            font-size: 12px;
        }

        .optional {
            color: #6b7280;
            font-size: 12px;
            font-weight: 400;
        }

        .form-input {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }

        .form-select:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .textarea {
            min-height: 120px;
            resize: vertical;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }

        .switch-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: #f9fafb;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }

        .switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: #d1d5db;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .switch.active {
            background: #4f46e5;
        }

        .switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .switch.active::before {
            transform: translateX(24px);
        }

        .permission-section {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
        }

        .permission-title {
            color: #92400e;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .permission-text {
            color: #78350f;
            font-size: 14px;
        }

        .sql-editor {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            border: 2px solid #374151;
            min-height: 200px;
            resize: vertical;
        }

        .sql-editor:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .variable-mapping {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 16px;
            border: 2px solid #d1d5db;
            margin-top: 16px;
        }

        .variable-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .variable-item:last-child {
            border-bottom: none;
        }

        .variable-name {
            font-weight: 500;
            color: #4f46e5;
            min-width: 120px;
        }

        .variable-arrow {
            color: #6b7280;
        }

        .variable-sql {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: #374151;
            background: white;
            padding: 4px 8px;
            border-radius: 4px;
            flex: 1;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding: 24px 32px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: transparent;
            color: #4f46e5;
            border: 2px solid #4f46e5;
        }

        .btn-outline:hover {
            background: #4f46e5;
            color: white;
        }

        .help-text {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        .test-section {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .test-title {
            color: #0369a1;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-test {
            background: #0ea5e9;
            color: white;
        }

        .btn-test:hover {
            background: #0284c7;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .content {
                padding: 20px;
            }
        }

        .params-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            min-height: 80px;
        }

        .param-item {
            display: flex;
            align-items: center;
            gap: 12px;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 12px 16px;
            transition: all 0.2s;
        }

        .param-settings-btn {
            background: #6366f1;
            color: white;
            border: none;
            border-radius: 4px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s;
            opacity: 0.8;
        }
        .param-settings-btn:hover {
            background: #4338ca;
            opacity: 1;
        }

        .param-required-switch {
            margin-left: 8px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .param-required-label {
            font-size: 12px;
            color: #4f46e5;
            font-weight: 500;
        }

        .permission-checkbox-group {
            display: flex;
            gap: 24px;
            flex-wrap: wrap;
            margin-top: 8px;
        }
        .permission-checkbox-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        .param-item:hover {
            border-color: #4f46e5;
            box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
        }

        .param-name {
            background: #f3f4f6;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            font-weight: 500;
            color: #1f2937;
            min-width: 120px;
            font-size: 14px;
        }

        .param-name:focus {
            outline: none;
            background: #e5e7eb;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }

        .param-placeholder {
            color: #9ca3af;
            font-size: 14px;
            flex: 1;
            cursor: pointer;
            display: flex;
            align-items: center;
            min-height: 20px;
        }

        .btn-add {
            background: #10b981;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-left: auto;
            transition: all 0.2s;
        }

        .btn-add:hover {
            background: #059669;
            transform: scale(1.1);
        }

        .btn-delete {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 4px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            opacity: 0.7;
        }

        .btn-delete:hover {
            background: #dc2626;
            opacity: 1;
            transform: scale(1.05);
        }

        /* 弹窗样式 */
        .modal-backdrop {
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(0,0,0,0.25);
            z-index: 1000;
            display: none;
        }
        .modal {
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            z-index: 1001;
            min-width: 380px;
            max-width: 90vw;
            padding: 28px 32px 20px 32px;
            display: none;
        }
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #4f46e5;
        }
        .modal-label {
            font-size: 14px;
            color: #374151;
            margin-bottom: 6px;
        }
        .modal-sql-editor {
            width: 100%;
            min-height: 90px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            border: 1.5px solid #d1d5db;
            border-radius: 6px;
            padding: 10px 12px;
            margin-bottom: 16px;
            background: #f9fafb;
            color: #22223b;
            resize: vertical;
        }
        .modal-actions {
            display: flex;
            gap: 16px;
            justify-content: flex-end;
        }
        .modal-btn {
            padding: 8px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        .modal-btn-primary {
            background: #4f46e5;
            color: white;
        }
        .modal-btn-primary:hover {
            background: #4338ca;
        }
        .modal-btn-cancel {
            background: #e5e7eb;
            color: #374151;
        }
        .modal-btn-cancel:hover {
            background: #d1d5db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="node-icon">🔍</div>
            <h1 id="nodeTitle" ondblclick="editTitle()" style="user-select: none; cursor: pointer;">数据查询器节点编辑器</h1>
            <input id="nodeTitleInput" style="display:none; font-size:24px; font-weight:600; border-radius:6px; padding:4px 12px; border:1.5px solid #4f46e5;" maxlength="32" />
            <div class="status-badge">活跃</div>
        </div>

        <div class="content">
            <!-- SQL配置（提前） -->
            <div class="section">
                <div class="section-title">
                    <div class="section-icon">💾</div>
                    SQL查询配置
                </div>
                <div class="form-group">
                    <label class="form-label">SQL查询语句</label>
                    <textarea class="sql-editor" id="sqlQuery" placeholder="-- 输入您的SQL查询语句&#10;SELECT * FROM defect_data &#10;WHERE product_id = :productId &#10;  AND lot_id = :lotId">-- 输入您的SQL查询语句
SELECT * FROM defect_data 
WHERE product_id = :productId 
  AND lot_id = :lotId</textarea>
                    <div class="help-text">使用 :参数名 格式引用输入变量</div>
                </div>

                <!-- 测试区域 -->
                <div class="test-section">
                    <div class="test-title">
                        🧪 查询测试
                    </div>
                    <button class="btn btn-test" onclick="testQuery()">测试查询</button>
                    <div id="testResult" style="margin-top: 12px; font-family: monospace; font-size: 12px; display: none;"></div>
                </div>
            </div>

            <!-- 输入参数 -->
            <div class="section">
                <div class="section-title">
                    <div class="section-icon">📥</div>
                    输入变量
                    <button class="btn-add" onclick="addInputParam()" title="添加参数">+</button>
                </div>
                <div class="params-container" id="inputParams">
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="LOT_ID" value="LOT_ID">
                        <div class="param-required-switch">
                            <span class="param-required-label">必填</span>
                            <div class="switch active" onclick="toggleRequired(this)"></div>
                        </div>
                        <button class="param-settings-btn" title="配置查询SQL" onclick="openVarSqlModal(this)">⚙️</button>
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="Time" value="Time">
                        <div class="param-required-switch">
                            <span class="param-required-label">必填</span>
                            <div class="switch active" onclick="toggleRequired(this)"></div>
                        </div>
                        <button class="param-settings-btn" title="配置查询SQL" onclick="openVarSqlModal(this)">⚙️</button>
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                </div>
            </div>

            <!-- 输出参数 -->
            <div class="section">
                <div class="section-title">
                    <div class="section-icon">📤</div>
                    输出变量
                    <button class="btn-add" onclick="addOutputParam()" title="添加参数">+</button>
                </div>
                <div class="params-container" id="outputParams">
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="comment" value="comment">
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="notice" value="notice">
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                </div>
            </div>

            <!-- 节点功能选项 -->
            <div class="section">
                <div class="section-title">
                    <div class="section-icon">⚙️</div>
                    节点功能选项
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <div class="switch-container">
                            <div>
                                <div class="form-label">需要用户手动反馈</div>
                                <div class="help-text">开启后节点执行完成需要用户确认</div>
                            </div>
                            <div class="switch" id="manualFeedback" onclick="toggleSwitch('manualFeedback')"></div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="switch-container">
                            <div>
                                <div class="form-label">节点自动执行</div>
                                <div class="help-text">开启后节点将自动执行，无需手动触发</div>
                            </div>
                            <div class="switch active" id="autoExecute" onclick="toggleSwitch('autoExecute')"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限设置 -->
            <div class="section">
                <div class="section-title">
                    <div class="section-icon">🔒</div>
                    权限设置
                </div>
                <div class="permission-section">
                    <div class="permission-title">
                        允许操作的用户组
                    </div>
                    <div class="permission-checkbox-group">
                        <label class="permission-checkbox-label"><input type="checkbox" class="permission-checkbox" value="YE" checked> YE</label>
                        <label class="permission-checkbox-label"><input type="checkbox" class="permission-checkbox" value="Module"> Module</label>
                        <label class="permission-checkbox-label"><input type="checkbox" class="permission-checkbox" value="QA"> QA</label>
                        <label class="permission-checkbox-label"><input type="checkbox" class="permission-checkbox" value="Admin"> Admin</label>
                    </div>
                </div>
            </div>

            <!-- 节点选项（issue动态交互开关） -->
            <div class="section">
                <div class="section-title">
                    <div class="section-icon">🧩</div>
                    节点选项
                </div>
                <div class="form-group">
                    <div class="switch-container">
                        <div>
                            <div class="form-label">issue中动态交互</div>
                            <div class="help-text">开启后节点可在issue中进行动态交互</div>
                        </div>
                        <div class="switch" id="issueDynamic" onclick="toggleSwitch('issueDynamic')"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-outline" onclick="resetForm()">重置</button>
            <button class="btn btn-secondary" onclick="saveAsDraft()">保存草稿</button>
            <button class="btn btn-primary" onclick="saveAndDeploy()">保存并部署</button>
        </div>
    </div>

    <!-- 弹窗HTML -->
    <div class="modal-backdrop" id="varSqlModalBackdrop"></div>
    <div class="modal" id="varSqlModal">
        <div class="modal-title">配置变量查询SQL</div>
        <div class="modal-label">变量名：<span id="modalVarName"></span></div>
        <textarea class="modal-sql-editor" id="modalVarSql" placeholder="为该变量配置候选筛选SQL，如 SELECT value FROM dict_table WHERE ..."></textarea>
        <div class="modal-actions">
            <button class="modal-btn modal-btn-cancel" onclick="closeVarSqlModal()">取消</button>
            <button class="modal-btn modal-btn-primary" onclick="saveVarSqlModal()">保存</button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001/api';

        // 切换必填开关
        function toggleRequired(switchElem) {
            switchElem.classList.toggle('active');
        }

        // 变量SQL弹窗相关
        let currentVarSqlTarget = null;
        function openVarSqlModal(btn) {
            // 找到对应param-item
            const paramItem = btn.closest('.param-item');
            currentVarSqlTarget = paramItem;
            // 变量名
            const varName = paramItem.querySelector('.param-name').value.trim() || '(未命名)';
            document.getElementById('modalVarName').textContent = varName;
            // 取querySql属性
            document.getElementById('modalVarSql').value = paramItem.dataset.querySql || '';
            // 显示弹窗
            document.getElementById('varSqlModalBackdrop').style.display = 'block';
            document.getElementById('varSqlModal').style.display = 'block';
        }
        function closeVarSqlModal() {
            document.getElementById('varSqlModalBackdrop').style.display = 'none';
            document.getElementById('varSqlModal').style.display = 'none';
            currentVarSqlTarget = null;
        }
        function saveVarSqlModal() {
            if (currentVarSqlTarget) {
                const sql = document.getElementById('modalVarSql').value;
                currentVarSqlTarget.dataset.querySql = sql;
            }
            closeVarSqlModal();
        }

        // 添加输入参数
        function addInputParam() {
            const container = document.getElementById('inputParams');
            const paramItem = document.createElement('div');
            paramItem.className = 'param-item';
            paramItem.innerHTML = `
                <input type="text" class="param-name" placeholder="参数名称" value="">
                <div class="param-required-switch">
                    <span class="param-required-label">必填</span>
                    <div class="switch active" onclick="toggleRequired(this)"></div>
                </div>
                <button class="param-settings-btn" title="配置查询SQL" onclick="openVarSqlModal(this)">⚙️</button>
                <span class="param-placeholder">设置变量值</span>
                <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
            `;
            paramItem.dataset.querySql = '';
            container.appendChild(paramItem);
        }

        // 添加输出参数
        function addOutputParam() {
            const container = document.getElementById('outputParams');
            const paramItem = document.createElement('div');
            paramItem.className = 'param-item';
            paramItem.innerHTML = `
                <input type="text" class="param-name" placeholder="参数名称" value="">
                <span class="param-placeholder">设置变量值</span>
                <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
            `;
            container.appendChild(paramItem);
        }

        // 删除参数
        function removeParam(button) {
            if (confirm('确定要删除这个参数吗？')) {
                button.parentElement.remove();
            }
        }

        // 切换开关状态
        function toggleSwitch(switchId) {
            const switchElement = document.getElementById(switchId);
            switchElement.classList.toggle('active');
        }

        // 标题双击编辑
        function editTitle() {
            const h1 = document.getElementById('nodeTitle');
            const input = document.getElementById('nodeTitleInput');
            input.value = h1.textContent;
            h1.style.display = 'none';
            input.style.display = 'inline-block';
            input.focus();
            input.select();
            input.onblur = saveTitle;
            input.onkeydown = function(e) {
                if (e.key === 'Enter') saveTitle();
                if (e.key === 'Escape') cancelTitle();
            };
        }
        function saveTitle() {
            const h1 = document.getElementById('nodeTitle');
            const input = document.getElementById('nodeTitleInput');
            h1.textContent = input.value.trim() || '数据查询器节点编辑器';
            h1.style.display = 'block';
            input.style.display = 'none';
        }
        function cancelTitle() {
            const h1 = document.getElementById('nodeTitle');
            const input = document.getElementById('nodeTitleInput');
            input.style.display = 'none';
            h1.style.display = 'block';
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置所有配置吗？此操作不可撤销。')) {
                // 重置输入参数
                const inputContainer = document.getElementById('inputParams');
                inputContainer.innerHTML = `
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="LOT_ID" value="LOT_ID">
                        <div class="param-required-switch">
                            <span class="param-required-label">必填</span>
                            <div class="switch active" onclick="toggleRequired(this)"></div>
                        </div>
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="Time" value="Time">
                        <div class="param-required-switch">
                            <span class="param-required-label">必填</span>
                            <div class="switch active" onclick="toggleRequired(this)"></div>
                        </div>
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                `;

                // 重置输出参数
                const outputContainer = document.getElementById('outputParams');
                outputContainer.innerHTML = `
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="comment" value="comment">
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                    <div class="param-item">
                        <input type="text" class="param-name" placeholder="notice" value="notice">
                        <span class="param-placeholder">设置变量值</span>
                        <button class="btn-delete" onclick="removeParam(this)" title="删除参数">🗑</button>
                    </div>
                `;
                
                // 重置开关状态
                document.getElementById('manualFeedback').classList.remove('active');
                document.getElementById('autoExecute').classList.add('active');
                
                // 重置默认SQL
                document.getElementById('sqlQuery').value = `-- 输入您的SQL查询语句\nSELECT * FROM defect_data \nWHERE product_id = :productId \n  AND lot_id = :lotId`;

                // 重置权限多选
                document.querySelectorAll('.permission-checkbox').forEach(cb => {
                    cb.checked = (cb.value === 'YE');
                });
                
                // 重置标题
                document.getElementById('nodeTitle').textContent = '数据查询器节点编辑器';
                document.getElementById('nodeTitleInput').value = '';
                // 重置issueDynamic
                document.getElementById('issueDynamic').classList.remove('active');
                
                alert('表单已重置');
            }
        }

        // 保存草稿
        function saveAsDraft() {
            const formData = collectFormData();
            localStorage.setItem('nodeConfigDraft', JSON.stringify(formData));
            console.log('保存草稿:', formData);
            alert('草稿已保存到本地存储');
        }

        // 保存并部署
        async function saveAndDeploy() {
            if (validateForm()) {
                const formData = collectFormData();
                
                try {
                    const response = await fetch(`${API_BASE_URL}/node/save`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-User-Group': 'YE'
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        alert('节点配置已保存并部署成功！');
                        console.log('保存成功:', result);
                    } else {
                        alert(`保存失败: ${result.error || '未知错误'}`);
                    }
                } catch (error) {
                    console.error('保存失败:', error);
                    alert('保存失败，请检查网络连接');
                }
            }
        }

        // 测试查询
        async function testQuery() {
            if (!validateForm()) {
                return;
            }

            const formData = collectFormData();
            const testButton = document.querySelector('.btn-test');
            const testResult = document.getElementById('testResult');
            
            testButton.textContent = '测试中...';
            testButton.disabled = true;
            testResult.style.display = 'block';
            testResult.innerHTML = '<div style="color: #0ea5e9;">正在执行查询...</div>';

            try {
                const response = await fetch(`${API_BASE_URL}/node/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    testResult.innerHTML = `
                        <div style="color: #059669; margin-bottom: 8px;">✅ 查询执行成功</div>
                        <div>📊 返回记录数: ${result.count}</div>
                        <div>⏱️ 执行时间: ${result.execution_time.toFixed(3)}秒</div>
                        <details style="margin-top: 8px;">
                            <summary style="cursor: pointer; color: #4f46e5;">查看数据详情</summary>
                            <pre style="background: #f3f4f6; padding: 8px; border-radius: 4px; margin-top: 4px; overflow-x: auto;">${JSON.stringify(result.data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    testResult.innerHTML = `
                        <div style="color: #dc2626; margin-bottom: 8px;">❌ 查询执行失败</div>
                        <div style="color: #dc2626;">${result.error || result.errors?.join(', ') || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                console.error('测试失败:', error);
                testResult.innerHTML = `
                    <div style="color: #dc2626; margin-bottom: 8px;">❌ 网络错误</div>
                    <div style="color: #dc2626;">无法连接到服务器，请检查后端服务是否启动</div>
                `;
            } finally {
                testButton.textContent = '测试查询';
                testButton.disabled = false;
            }
        }

        // 收集表单数据
        function collectFormData() {
            // 收集输入参数
            const inputParams = [];
            document.querySelectorAll('#inputParams .param-item').forEach(item => {
                const paramName = item.querySelector('.param-name').value.trim();
                const required = item.querySelector('.switch').classList.contains('active');
                const querySql = item.dataset.querySql || '';
                if (paramName) {
                    inputParams.push({ name: paramName, required, querySql });
                }
            });

            // 收集输出参数
            const outputParams = {};
            document.querySelectorAll('#outputParams .param-item').forEach(item => {
                const paramName = item.querySelector('.param-name').value.trim();
                if (paramName) {
                    outputParams[paramName] = null; // 只保存参数名，不设置值
                }
            });

            // 收集权限
            const permissions = Array.from(document.querySelectorAll('.permission-checkbox:checked')).map(cb => cb.value);

            return {
                nodeTitle: document.getElementById('nodeTitle').textContent.trim(),
                inputParams: inputParams,
                outputParams: outputParams,
                permissions: permissions,
                sqlConfig: {
                    query: document.getElementById('sqlQuery').value
                },
                nodeOptions: {
                    manualFeedback: document.getElementById('manualFeedback').classList.contains('active'),
                    autoExecute: document.getElementById('autoExecute').classList.contains('active'),
                    issueDynamic: document.getElementById('issueDynamic').classList.contains('active')
                }
            };
        }

        // 验证表单
        function validateForm() {
            // 检查是否有输入参数
            const inputParamNames = Array.from(document.querySelectorAll('#inputParams .param-name'))
                .map(input => input.value.trim())
                .filter(name => name);

            if (inputParamNames.length === 0) {
                alert('请至少添加一个输入参数');
                return false;
            }

            // 检查SQL查询
            const sqlQuery = document.getElementById('sqlQuery').value.trim();
            if (!sqlQuery) {
                alert('请输入SQL查询语句');
                return false;
            }

            // 检查参数名重复
            const allParamNames = [
                ...Array.from(document.querySelectorAll('#inputParams .param-name')).map(input => input.value.trim()),
                ...Array.from(document.querySelectorAll('#outputParams .param-name')).map(input => input.value.trim())
            ].filter(name => name);

            const uniqueNames = new Set(allParamNames);
            if (uniqueNames.size !== allParamNames.length) {
                alert('参数名不能重复');
                return false;
            }

            return true;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从本地存储恢复草稿
            const savedDraft = localStorage.getItem('nodeConfigDraft');
            if (savedDraft) {
                try {
                    const draft = JSON.parse(savedDraft);
                    console.log('发现保存的草稿:', draft);
                    // 这里可以实现恢复草稿的逻辑
                } catch (e) {
                    console.log('草稿数据格式错误，忽略');
                }
            }
        });
    </script>
</body>
</html> 