#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动应用程序
"""

import sys
import os
sys.path.append('issue_management')

from issue_management.app import app, init_database

if __name__ == '__main__':
    print("🚀 启动DN Issue Management System...")
    
    # 初始化数据库
    print("📊 初始化数据库...")
    init_database()
    print("✅ 数据库初始化完成")
    
    # 启动应用
    print("🌐 启动Web服务器...")
    print("📍 访问地址: http://localhost:3000")
    print("🔑 默认登录: admin / admin123")
    
    app.run(debug=True, host='0.0.0.0', port=3000)
