#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新工作流节点位置的脚本
"""

import sqlite3
import json
import os

def update_workflow_positions():
    """更新工作流节点位置"""
    
    # 数据库路径
    db_path = 'issue_management/dn_issues.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print("🔄 开始更新工作流节点位置...")
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    try:
        # 获取当前的 dn-rd 工作流
        workflow = conn.execute('SELECT * FROM workflows WHERE id = ?', ('dn-rd',)).fetchone()
        
        if not workflow:
            print("❌ 未找到 dn-rd 工作流")
            return
        
        print(f"✅ 找到工作流: {workflow['name']}")
        
        # 解析当前配置
        config = json.loads(workflow['config'])
        
        # 更新节点位置
        position_updates = {
            'hold_lot_query': {'x': 250, 'y': 50},
            'hold_lot_display': {'x': 250, 'y': 180},
            'case_info': {'x': 250, 'y': 310},
            'scan_info_query': {'x': 50, 'y': 440},
            'lot_history_query': {'x': 180, 'y': 440},
            'map_gallery_query': {'x': 320, 'y': 440},
            'trend_chart_query': {'x': 450, 'y': 440},
            'scan_info_display': {'x': 50, 'y': 570},
            'lot_history_display': {'x': 180, 'y': 570},
            'map_gallery_display': {'x': 320, 'y': 570},
            'trend_chart_display': {'x': 450, 'y': 570},
            'case_confirm': {'x': 250, 'y': 700},
            'ye_comment': {'x': 180, 'y': 830},
            'ppt_preview': {'x': 320, 'y': 830},
            'send_email': {'x': 250, 'y': 960}
        }
        
        # 更新节点位置
        updated_count = 0
        for node in config.get('nodes', []):
            node_id = node.get('id')
            if node_id in position_updates:
                old_position = node.get('position', {})
                new_position = position_updates[node_id]
                node['position'] = new_position
                print(f"  📍 更新节点 {node_id}: {old_position} → {new_position}")
                updated_count += 1
        
        # 保存更新后的配置
        updated_config = json.dumps(config)
        conn.execute(
            'UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            (updated_config, 'dn-rd')
        )
        
        # 同样更新 dn-production 工作流
        production_workflow = conn.execute('SELECT * FROM workflows WHERE id = ?', ('dn-production',)).fetchone()
        if production_workflow:
            production_config = json.loads(production_workflow['config'])
            for node in production_config.get('nodes', []):
                node_id = node.get('id')
                if node_id in position_updates:
                    node['position'] = position_updates[node_id]
            
            updated_production_config = json.dumps(production_config)
            conn.execute(
                'UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                (updated_production_config, 'dn-production')
            )
            print(f"✅ 同时更新了 dn-production 工作流")
        
        conn.commit()
        print(f"✅ 成功更新了 {updated_count} 个节点的位置")
        print("🎉 工作流位置更新完成！")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    update_workflow_positions()
