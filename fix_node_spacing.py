#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复前三个节点间距的脚本
"""

import sqlite3
import json

def fix_node_spacing():
    """修复节点间距"""
    print("🔧 开始修复节点间距...")
    
    # 连接数据库
    db_path = 'issue_management/dn_issues.db'
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    try:
        # 获取 dn-rd 工作流
        workflow = conn.execute('SELECT * FROM workflows WHERE id = ?', ('dn-rd',)).fetchone()
        
        if not workflow:
            print("❌ 未找到 dn-rd 工作流")
            return
        
        print(f"✅ 找到工作流: {workflow['name']}")
        
        # 解析配置
        config = json.loads(workflow['config'])
        nodes = config.get('nodes', [])
        
        # 显示当前位置
        print("\n📍 当前节点位置:")
        for node in nodes[:3]:  # 只显示前三个
            pos = node.get('position', {})
            print(f"   {node['icon']} - {node['title']}: y={pos.get('y', 0)}")
        
        # 更新前三个节点的位置
        position_updates = {
            'hold_lot_query': {'x': 250, 'y': 50},      # HL 保持不变
            'hold_lot_display': {'x': 250, 'y': 180},   # HD: 150 → 180 (+30)
            'case_info': {'x': 250, 'y': 310}           # CI: 250 → 310 (+60)
        }
        
        # 后续节点也需要相应调整
        other_updates = {
            'scan_info_query': {'x': 50, 'y': 440},
            'lot_history_query': {'x': 180, 'y': 440},
            'map_gallery_query': {'x': 320, 'y': 440},
            'trend_chart_query': {'x': 450, 'y': 440},
            'scan_info_display': {'x': 50, 'y': 570},
            'lot_history_display': {'x': 180, 'y': 570},
            'map_gallery_display': {'x': 320, 'y': 570},
            'trend_chart_display': {'x': 450, 'y': 570},
            'case_confirm': {'x': 250, 'y': 700},
            'ye_comment': {'x': 180, 'y': 830},
            'ppt_preview': {'x': 320, 'y': 830},
            'send_email': {'x': 250, 'y': 960}
        }
        
        # 合并所有更新
        all_updates = {**position_updates, **other_updates}
        
        # 应用更新
        updated_count = 0
        for node in nodes:
            node_id = node.get('id')
            if node_id in all_updates:
                old_pos = node.get('position', {})
                new_pos = all_updates[node_id]
                node['position'] = new_pos
                print(f"   📍 {node['icon']}: y={old_pos.get('y', 0)} → y={new_pos['y']}")
                updated_count += 1
        
        # 保存更新后的配置
        updated_config = json.dumps(config)
        conn.execute(
            'UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            (updated_config, 'dn-rd')
        )
        
        # 同样更新 dn-production 工作流
        production_workflow = conn.execute('SELECT * FROM workflows WHERE id = ?', ('dn-production',)).fetchone()
        if production_workflow:
            production_config = json.loads(production_workflow['config'])
            for node in production_config.get('nodes', []):
                node_id = node.get('id')
                if node_id in all_updates:
                    node['position'] = all_updates[node_id]
            
            updated_production_config = json.dumps(production_config)
            conn.execute(
                'UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                (updated_production_config, 'dn-production')
            )
            print(f"✅ 同时更新了 dn-production 工作流")
        
        conn.commit()
        print(f"\n🎉 成功更新了 {updated_count} 个节点的位置")
        print("💡 现在重启应用，前三个绿色节点的间距应该更合理了！")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    fix_node_spacing()
