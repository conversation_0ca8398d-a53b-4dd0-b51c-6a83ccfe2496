#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复节点间距问题
"""

import sqlite3
import json

def fix_spacing():
    print("🔧 开始修复节点间距...")
    
    # 连接数据库
    db_path = 'dn_product-master/dn_issues.db'
    conn = sqlite3.connect(db_path)
    
    try:
        # 获取当前工作流配置
        cursor = conn.cursor()
        cursor.execute('SELECT config FROM workflows WHERE id = ?', ('dn-rd',))
        result = cursor.fetchone()
        
        if not result:
            print("❌ 未找到工作流")
            return
        
        config = json.loads(result[0])
        print("✅ 找到工作流配置")
        
        # 显示当前前三个节点的位置
        print("\n📍 当前前三个节点位置:")
        for node in config['nodes']:
            if node['id'] in ['hold_lot_query', 'hold_lot_display', 'case_info']:
                y = node['position']['y']
                print(f"   {node['icon']} - {node['title']}: y={y}")
        
        # 更新节点位置 - 增加间距
        updates = {
            'hold_lot_query': {'x': 250, 'y': 50},      # HL 保持不变
            'hold_lot_display': {'x': 250, 'y': 180},   # HD: 增加间距
            'case_info': {'x': 250, 'y': 310},          # CI: 增加间距
            # 后续节点也要相应调整
            'scan_info_query': {'x': 50, 'y': 440},
            'lot_history_query': {'x': 180, 'y': 440},
            'map_gallery_query': {'x': 320, 'y': 440},
            'trend_chart_query': {'x': 450, 'y': 440},
            'scan_info_display': {'x': 50, 'y': 570},
            'lot_history_display': {'x': 180, 'y': 570},
            'map_gallery_display': {'x': 320, 'y': 570},
            'trend_chart_display': {'x': 450, 'y': 570},
            'case_confirm': {'x': 250, 'y': 700},
            'ye_comment': {'x': 180, 'y': 830},
            'ppt_preview': {'x': 320, 'y': 830},
            'send_email': {'x': 250, 'y': 960}
        }
        
        # 应用更新
        print("\n🔄 更新节点位置:")
        for node in config['nodes']:
            if node['id'] in updates:
                old_y = node['position']['y']
                new_pos = updates[node['id']]
                node['position'] = new_pos
                print(f"   {node['icon']}: y={old_y} → y={new_pos['y']}")
        
        # 保存到数据库
        updated_config = json.dumps(config)
        cursor.execute('UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', 
                      (updated_config, 'dn-rd'))
        
        # 同时更新 dn-production 工作流
        cursor.execute('SELECT config FROM workflows WHERE id = ?', ('dn-production',))
        prod_result = cursor.fetchone()
        if prod_result:
            prod_config = json.loads(prod_result[0])
            for node in prod_config['nodes']:
                if node['id'] in updates:
                    node['position'] = updates[node['id']]
            cursor.execute('UPDATE workflows SET config = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', 
                          (json.dumps(prod_config), 'dn-production'))
            print("✅ 同时更新了 dn-production 工作流")
        
        conn.commit()
        print("\n🎉 节点位置更新完成！")
        print("💡 请刷新浏览器页面查看效果")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == '__main__':
    fix_spacing()
